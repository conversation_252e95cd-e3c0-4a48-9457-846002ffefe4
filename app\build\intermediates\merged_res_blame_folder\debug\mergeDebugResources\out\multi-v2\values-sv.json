{"logs": [{"outputFile": "com.example.camera.app-mergeDebugResources-52:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,319,422,550,630,722,832,972,1092,1243,1324,1420,1506,1596,1706,1824,1925,2054,2177,2308,2476,2600,2714,2837,2954,3041,3135,3249,3384,3476,3580,3679,3807,3946,4048,4140,4216,4290,4370,4451,4548,4624,4705,4803,4899,4994,5091,5174,5274,5371,5470,5588,5664,5764", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "185,314,417,545,625,717,827,967,1087,1238,1319,1415,1501,1591,1701,1819,1920,2049,2172,2303,2471,2595,2709,2832,2949,3036,3130,3244,3379,3471,3575,3674,3802,3941,4043,4135,4211,4285,4365,4446,4543,4619,4700,4798,4894,4989,5086,5169,5269,5366,5465,5583,5659,5759,5854"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2778,2913,3042,3145,4182,4262,4354,4464,4604,4724,4875,4956,5052,5138,5228,5338,5456,5557,5686,5809,5940,6108,6232,6346,6469,6586,6673,6767,6881,7016,7108,7212,7311,7439,7578,7680,7967,8131,8843,9071,9253,9615,9691,9772,9870,9966,10061,10158,10241,10341,10438,10537,10655,10731,10831", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "2908,3037,3140,3268,4257,4349,4459,4599,4719,4870,4951,5047,5133,5223,5333,5451,5552,5681,5804,5935,6103,6227,6341,6464,6581,6668,6762,6876,7011,7103,7207,7306,7434,7573,7675,7767,8038,8200,8918,9147,9345,9686,9767,9865,9961,10056,10153,10236,10336,10433,10532,10650,10726,10826,10921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4001,4094,7772,7868,8043,8205,8281,8369,8458,8539,8603,8667,8753,9002,9350,9428,9495", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "4089,4177,7863,7962,8126,8276,8364,8453,8534,8598,8662,8748,8838,9066,9423,9490,9610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3273,3368,3470,3568,3667,3775,3880,9152", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3363,3465,3563,3662,3770,3875,3996,9248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,874,967,1061,1156,1250,1353,1448,1545,1643,1739,1832,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,75,92,93,94,93,102,94,96,97,95,92,78,105,98,95,104,102,101,153,101,78", "endOffsets": "203,306,417,501,603,716,793,869,962,1056,1151,1245,1348,1443,1540,1638,1734,1827,1906,2012,2111,2207,2312,2415,2517,2671,2773,2852"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,874,967,1061,1156,1250,1353,1448,1545,1643,1739,1832,1911,2017,2116,2212,2317,2420,2522,2676,8923", "endColumns": "102,102,110,83,101,112,76,75,92,93,94,93,102,94,96,97,95,92,78,105,98,95,104,102,101,153,101,78", "endOffsets": "203,306,417,501,603,716,793,869,962,1056,1151,1245,1348,1443,1540,1638,1734,1827,1906,2012,2111,2207,2312,2415,2517,2671,2773,8997"}}]}]}