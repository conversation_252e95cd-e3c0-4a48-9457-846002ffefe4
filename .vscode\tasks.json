{"version": "2.0.0", "tasks": [{"label": "Clean Build", "type": "shell", "command": "gradlew", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "Build Debug APK", "type": "shell", "command": "gradlew", "args": ["assembleDebug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "Build Release APK", "type": "shell", "command": "gradlew", "args": ["assembleRelease"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "Install Debug on Device", "type": "shell", "command": "gradlew", "args": ["installDebug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "Uninstall from Device", "type": "shell", "command": "gradlew", "args": ["uninstallDebug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "Check Dependencies", "type": "shell", "command": "gradlew", "args": ["dependencies"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}]}