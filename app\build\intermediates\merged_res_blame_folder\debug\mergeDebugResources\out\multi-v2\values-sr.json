{"logs": [{"outputFile": "com.example.camera.app-mergeDebugResources-52:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,275,398,508,588,678,786,918,1033,1173,1254,1350,1441,1535,1647,1768,1869,2006,2142,2271,2447,2568,2684,2806,2925,3017,3111,3224,3350,3446,3544,3649,3786,3931,4036,4134,4207,4287,4372,4456,4559,4635,4714,4807,4906,4995,5089,5172,5276,5369,5466,5595,5671,5772", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "160,270,393,503,583,673,781,913,1028,1168,1249,1345,1436,1530,1642,1763,1864,2001,2137,2266,2442,2563,2679,2801,2920,3012,3106,3219,3345,3441,3539,3644,3781,3926,4031,4129,4202,4282,4367,4451,4554,4630,4709,4802,4901,4990,5084,5167,5271,5364,5461,5590,5666,5767,5860"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2833,2943,3053,3176,4196,4276,4366,4474,4606,4721,4861,4942,5038,5129,5223,5335,5456,5557,5694,5830,5959,6135,6256,6372,6494,6613,6705,6799,6912,7038,7134,7232,7337,7474,7619,7724,8020,8179,8910,9154,9339,9709,9785,9864,9957,10056,10145,10239,10322,10426,10519,10616,10745,10821,10922", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "2938,3048,3171,3281,4271,4361,4469,4601,4716,4856,4937,5033,5124,5218,5330,5451,5552,5689,5825,5954,6130,6251,6367,6489,6608,6700,6794,6907,7033,7129,7227,7332,7469,7614,7719,7817,8088,8254,8990,9233,9437,9780,9859,9952,10051,10140,10234,10317,10421,10514,10611,10740,10816,10917,11010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4012,4109,7822,7919,8093,8259,8336,8427,8519,8604,8675,8745,8825,9081,9442,9521,9591", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "4104,4191,7914,8015,8174,8331,8422,8514,8599,8670,8740,8820,8905,9149,9516,9586,9704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3286,3384,3486,3583,3687,3791,3896,9238", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3379,3481,3578,3682,3786,3891,4007,9334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,2914"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,897,988,1080,1175,1269,1370,1463,1558,1663,1754,1845,1930,2035,2141,2244,2350,2459,2566,2736,8995", "endColumns": "106,100,105,85,103,121,83,81,90,91,94,93,100,92,94,104,90,90,84,104,105,102,105,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,810,892,983,1075,1170,1264,1365,1458,1553,1658,1749,1840,1925,2030,2136,2239,2345,2454,2561,2731,2828,9076"}}]}]}