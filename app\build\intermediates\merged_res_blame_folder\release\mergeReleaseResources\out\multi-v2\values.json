{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f52d47342be363b65f536ca25226a98b\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "285", "startColumns": "4", "startOffsets": "18033", "endColumns": "49", "endOffsets": "18078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,180,181,182,183,184,185,186,187,188,204,205,206,207,208,209,210,211,247,248,249,250,255,261,262,264,281,287,288,289,290,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,385,407,408,409,410,411,412,420,421,425,429,433,438,444,451,455,459,464,468,472,476,480,484,488,494,498,504,508,514,518,523,527,530,534,540,544,550,554,560,563,567,571,575,579,583,584,585,586,589,592,595,598,602,603,604,605,606,609,611,613,615,620,621,625,631,635,636,638,649,650,654,660,664,665,666,670,697,701,702,706,734,904,930,1101,1127,1158,1166,1172,1186,1208,1213,1218,1228,1237,1246,1250,1257,1265,1272,1273,1282,1285,1288,1292,1296,1300,1303,1304,1309,1314,1324,1329,1336,1342,1343,1346,1350,1355,1357,1359,1362,1365,1367,1371,1374,1381,1384,1387,1391,1393,1397,1399,1401,1403,1407,1415,1423,1435,1441,1450,1453,1464,1467,1468,1473,1474,1489,1558,1628,1629,1639,1648,1649,1651,1655,1658,1661,1664,1667,1670,1673,1676,1680,1683,1686,1689,1693,1696,1700,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1726,1728,1729,1730,1731,1732,1733,1734,1735,1737,1738,1740,1741,1743,1745,1746,1748,1749,1750,1751,1752,1753,1755,1756,1757,1758,1759,1771,1773,1775,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1791,1792,1793,1794,1795,1796,1798,1802,1807,1808,1809,1810,1811,1812,1816,1817,1818,1819,1821,1823,1825,1827,1829,1830,1831,1832,1834,1836,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1852,1853,1854,1855,1857,1859,1860,1862,1863,1865,1867,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1882,1883,1884,1885,1887,1888,1889,1890,1891,1893,1895,1897,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1916,1991,1994,1997,2000,2014,2020,2062,2091,2118,2127,2189,2548,2568,2596,2707,2731,2737,2743,2764,2888,2908,2914,2922,2928,2963,2995,3061,3081,3136,3148,3174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,2004,2081,2159,2265,2371,2450,2530,2587,2776,2850,2925,2990,3056,3116,3177,3249,3322,3389,3457,3516,3575,3634,3693,3752,3806,3860,3913,3967,4021,4075,4261,4335,4414,4487,4561,4632,4704,4776,4990,5047,5105,5178,5252,5326,5401,5473,5546,5616,5777,5837,5940,6009,6078,6148,6222,6298,6362,6439,6515,6592,6657,6726,6803,6878,6947,7015,7092,7158,7219,7316,7381,7450,7549,7620,7679,7737,7794,7853,7917,7988,8060,8132,8204,8276,8343,8411,8479,8538,8601,8665,8755,8846,8906,8972,9039,9105,9175,9239,9292,9359,9420,9487,9600,9658,9721,9786,9851,9926,9999,10071,10120,10181,10242,10303,10365,10429,10493,10557,10622,10685,10745,10806,10872,10931,10991,11053,11124,11184,11740,11826,11913,12003,12090,12178,12260,12343,12433,13502,13554,13612,13657,13723,13787,13844,13901,16078,16135,16183,16232,16521,16801,16848,16939,17844,18136,18200,18262,18322,18449,18523,18593,18671,18725,18795,18880,18928,18974,19035,19098,19164,19228,19299,19362,19427,19491,19552,19613,19665,19738,19812,19881,19956,20030,20104,20245,24818,26161,26239,26329,26417,26513,26603,27185,27274,27521,27802,28054,28339,28732,29209,29431,29653,29929,30156,30386,30616,30846,31076,31303,31722,31948,32373,32603,33031,33250,33533,33741,33872,34099,34525,34750,35177,35398,35823,35943,36219,36520,36844,37135,37449,37586,37717,37822,38064,38231,38435,38643,38914,39026,39138,39243,39360,39574,39720,39860,39946,40294,40382,40628,41046,41295,41377,41475,42067,42167,42419,42843,43098,43192,43281,43518,45542,45784,45886,46139,48295,58827,60343,70974,72502,74259,74885,75305,76366,77631,77887,78123,78670,79164,79769,79967,80547,81111,81486,81604,82142,82299,82495,82768,83024,83194,83335,83399,83764,84131,84807,85071,85409,85762,85856,86042,86348,86610,86735,86862,87101,87312,87431,87624,87801,88256,88437,88559,88818,88931,89118,89220,89327,89456,89731,90239,90735,91612,91906,92476,92625,93357,93529,93613,93949,94041,94737,99983,105372,105434,106012,106596,106687,106800,107029,107189,107341,107512,107678,107847,108014,108177,108420,108590,108763,108934,109208,109407,109612,109942,110026,110122,110218,110316,110416,110518,110620,110722,110824,110926,111026,111122,111234,111363,111486,111617,111748,111846,111960,112054,112194,112328,112424,112536,112636,112752,112848,112960,113060,113200,113336,113500,113630,113788,113938,114079,114223,114358,114470,114620,114748,114876,115012,115144,115274,115404,115516,116414,116560,116704,116842,116908,116998,117074,117178,117268,117370,117478,117586,117686,117766,117858,117956,118066,118144,118250,118342,118446,118556,118678,118841,119081,119161,119261,119351,119461,119551,119792,119886,119992,120084,120184,120296,120410,120526,120642,120736,120850,120962,121064,121184,121306,121388,121492,121612,121738,121836,121930,122018,122130,122246,122368,122480,122655,122771,122857,122949,123061,123185,123252,123378,123446,123574,123718,123846,123915,124010,124125,124238,124337,124446,124557,124668,124769,124874,124974,125104,125195,125318,125412,125524,125610,125714,125810,125898,126016,126120,126224,126350,126438,126546,126646,126736,126846,126930,127032,127116,127170,127234,127340,127426,127536,127620,127879,130495,130613,130728,130808,131169,131402,132806,134150,135511,135899,138674,148578,149217,150574,154407,155158,155420,155620,155999,160277,160883,161112,161406,161621,162704,163554,166580,167324,169455,169795,171106", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,92,93,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,180,181,182,183,184,185,186,187,188,204,205,206,207,208,209,210,211,247,248,249,250,255,261,262,264,281,287,288,289,290,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,385,407,408,409,410,411,419,420,424,428,432,437,443,450,454,458,463,467,471,475,479,483,487,493,497,503,507,513,517,522,526,529,533,539,543,549,553,559,562,566,570,574,578,582,583,584,585,588,591,594,597,601,602,603,604,605,608,610,612,614,619,620,624,630,634,635,637,648,649,653,659,663,664,665,669,696,700,701,705,733,903,929,1100,1126,1157,1165,1171,1185,1207,1212,1217,1227,1236,1245,1249,1256,1264,1271,1272,1281,1284,1287,1291,1295,1299,1302,1303,1308,1313,1323,1328,1335,1341,1342,1345,1349,1354,1356,1358,1361,1364,1366,1370,1373,1380,1383,1386,1390,1392,1396,1398,1400,1402,1406,1414,1422,1434,1440,1449,1452,1463,1466,1467,1472,1473,1478,1557,1627,1628,1638,1647,1648,1650,1654,1657,1660,1663,1666,1669,1672,1675,1679,1682,1685,1688,1692,1695,1699,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1725,1727,1728,1729,1730,1731,1732,1733,1734,1736,1737,1739,1740,1742,1744,1745,1747,1748,1749,1750,1751,1752,1754,1755,1756,1757,1758,1759,1772,1774,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1790,1791,1792,1793,1794,1795,1797,1801,1805,1807,1808,1809,1810,1811,1815,1816,1817,1818,1820,1822,1824,1826,1828,1829,1830,1831,1833,1835,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1851,1852,1853,1854,1856,1858,1859,1861,1862,1864,1866,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1881,1882,1883,1884,1886,1887,1888,1889,1890,1892,1894,1896,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1990,1993,1996,1999,2013,2019,2029,2090,2117,2126,2188,2547,2551,2595,2613,2730,2736,2742,2763,2887,2907,2913,2917,2927,2962,2974,3060,3080,3135,3147,3173,3180", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2076,2154,2260,2366,2445,2525,2582,2640,2845,2920,2985,3051,3111,3172,3244,3317,3384,3452,3511,3570,3629,3688,3747,3801,3855,3908,3962,4016,4070,4124,4330,4409,4482,4556,4627,4699,4771,4844,5042,5100,5173,5247,5321,5396,5468,5541,5611,5682,5832,5893,6004,6073,6143,6217,6293,6357,6434,6510,6587,6652,6721,6798,6873,6942,7010,7087,7153,7214,7311,7376,7445,7544,7615,7674,7732,7789,7848,7912,7983,8055,8127,8199,8271,8338,8406,8474,8533,8596,8660,8750,8841,8901,8967,9034,9100,9170,9234,9287,9354,9415,9482,9595,9653,9716,9781,9846,9921,9994,10066,10115,10176,10237,10298,10360,10424,10488,10552,10617,10680,10740,10801,10867,10926,10986,11048,11119,11179,11247,11821,11908,11998,12085,12173,12255,12338,12428,12519,13549,13607,13652,13718,13782,13839,13896,13950,16130,16178,16227,16278,16550,16843,16892,16980,17871,18195,18257,18317,18374,18518,18588,18666,18720,18790,18875,18923,18969,19030,19093,19159,19223,19294,19357,19422,19486,19547,19608,19660,19733,19807,19876,19951,20025,20099,20240,20310,24866,26234,26324,26412,26508,26598,27180,27269,27516,27797,28049,28334,28727,29204,29426,29648,29924,30151,30381,30611,30841,31071,31298,31717,31943,32368,32598,33026,33245,33528,33736,33867,34094,34520,34745,35172,35393,35818,35938,36214,36515,36839,37130,37444,37581,37712,37817,38059,38226,38430,38638,38909,39021,39133,39238,39355,39569,39715,39855,39941,40289,40377,40623,41041,41290,41372,41470,42062,42162,42414,42838,43093,43187,43276,43513,45537,45779,45881,46134,48290,58822,60338,70969,72497,74254,74880,75300,76361,77626,77882,78118,78665,79159,79764,79962,80542,81106,81481,81599,82137,82294,82490,82763,83019,83189,83330,83394,83759,84126,84802,85066,85404,85757,85851,86037,86343,86605,86730,86857,87096,87307,87426,87619,87796,88251,88432,88554,88813,88926,89113,89215,89322,89451,89726,90234,90730,91607,91901,92471,92620,93352,93524,93608,93944,94036,94314,99978,105367,105429,106007,106591,106682,106795,107024,107184,107336,107507,107673,107842,108009,108172,108415,108585,108758,108929,109203,109402,109607,109937,110021,110117,110213,110311,110411,110513,110615,110717,110819,110921,111021,111117,111229,111358,111481,111612,111743,111841,111955,112049,112189,112323,112419,112531,112631,112747,112843,112955,113055,113195,113331,113495,113625,113783,113933,114074,114218,114353,114465,114615,114743,114871,115007,115139,115269,115399,115511,115651,116555,116699,116837,116903,116993,117069,117173,117263,117365,117473,117581,117681,117761,117853,117951,118061,118139,118245,118337,118441,118551,118673,118836,118993,119156,119256,119346,119456,119546,119787,119881,119987,120079,120179,120291,120405,120521,120637,120731,120845,120957,121059,121179,121301,121383,121487,121607,121733,121831,121925,122013,122125,122241,122363,122475,122650,122766,122852,122944,123056,123180,123247,123373,123441,123569,123713,123841,123910,124005,124120,124233,124332,124441,124552,124663,124764,124869,124969,125099,125190,125313,125407,125519,125605,125709,125805,125893,126011,126115,126219,126345,126433,126541,126641,126731,126841,126925,127027,127111,127165,127229,127335,127421,127531,127615,127735,130490,130608,130723,130803,131164,131397,131914,134145,135506,135894,138669,148573,148708,150569,151141,155153,155415,155615,155994,160272,160878,161107,161258,161616,162699,163011,166575,167319,169450,169790,171101,171304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3aea1891d23fd327287f58962aca339d\\transformed\\camera-view-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,2918", "startColumns": "4,4,4", "startOffsets": "250,511,161263", "endLines": "7,17,2921", "endColumns": "11,11,24", "endOffsets": "397,813,161401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\229fb45d41e6a7dc95d863a72eae7b4c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "257,260", "startColumns": "4,4", "startOffsets": "16610,16734", "endColumns": "53,66", "endOffsets": "16659,16796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d4c5419693f837eebddb887747f95a1b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "319", "startColumns": "4", "startOffsets": "20315", "endColumns": "82", "endOffsets": "20393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,251,252,253,254,256,286,332,333,370,371,373,375,376,378,379,380,381,382,383,386,390,391,392,1479,1482,1485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14168,14227,14286,14346,14406,14466,14526,14586,14646,14706,14766,14826,14886,14945,15005,15065,15125,15185,15245,15305,15365,15425,15485,15545,15604,15664,15724,15783,15842,15901,15960,16019,16283,16357,16415,16470,16555,18083,21276,21341,23979,24045,24190,24294,24346,24475,24537,24591,24627,24661,24711,24871,25109,25156,25192,94319,94431,94542", "endLines": "215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,251,252,253,254,256,286,332,333,370,371,373,375,376,378,379,380,381,382,383,386,390,391,392,1481,1484,1488", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "14222,14281,14341,14401,14461,14521,14581,14641,14701,14761,14821,14881,14940,15000,15060,15120,15180,15240,15300,15360,15420,15480,15540,15599,15659,15719,15778,15837,15896,15955,16014,16073,16352,16410,16465,16516,16605,18131,21336,21390,24040,24141,24243,24341,24401,24532,24586,24622,24656,24706,24760,24912,25151,25187,25277,94426,94537,94732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "321,322,323,324,334,335,336,337,338,339,342,343,344,345,346,347,348,349,350,351,352,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,372,374,377,384,387,389,393,394,395,396,397,398,399,400,401,402,403,404,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20442,20526,20608,20685,21395,21443,21504,21583,21685,21767,21883,21933,21998,22055,22120,22205,22296,22366,22459,22548,22642,22787,22874,22958,23050,23144,23204,23268,23351,23441,23504,23572,23640,23737,23842,23914,24146,24248,24406,24765,24917,25041,25282,25328,25378,25445,25512,25578,25643,25697,25769,25836,25906,25988,26034,26100", "endLines": "321,322,323,324,334,335,336,337,338,341,342,343,344,345,346,347,348,349,350,351,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,372,374,377,384,387,389,393,394,395,396,397,398,399,400,401,402,403,404,405,406", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "20521,20603,20680,20760,21438,21499,21578,21680,21762,21878,21928,21993,22050,22115,22200,22291,22361,22454,22543,22637,22782,22869,22953,23045,23139,23199,23263,23346,23436,23499,23567,23635,23732,23837,23909,23974,24185,24289,24470,24813,24965,25104,25323,25373,25440,25507,25573,25638,25692,25764,25831,25901,25983,26029,26095,26156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\45321f74a8e4ea570c23332585254c7b\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "282", "startColumns": "4", "startOffsets": "17876", "endColumns": "42", "endOffsets": "17914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,43,44,67,68,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,212,213,214,258,259,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,291,325,326,327,328,329,330,331,388,1760,1761,1765,1766,1770,1914,1915,2552,2558,2614,2647,2668,2701", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2645,2710,4129,4198,11252,11322,11390,11462,11532,11593,11667,12524,12585,12646,12708,12772,12834,12895,12963,13063,13123,13189,13262,13331,13388,13440,13955,14027,14103,16664,16699,16985,17040,17103,17158,17216,17274,17335,17398,17455,17506,17556,17617,17674,17740,17774,17809,18379,20765,20832,20904,20973,21042,21116,21188,24970,115656,115773,115974,116084,116285,127740,127812,148713,148916,151146,152877,153558,154240", "endLines": "9,28,29,43,44,67,68,173,174,175,176,177,178,179,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,212,213,214,258,259,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,291,325,326,327,328,329,330,331,388,1760,1764,1765,1769,1770,1914,1915,2557,2567,2646,2667,2700,2706", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1550,1638,2705,2771,4193,4256,11317,11385,11457,11527,11588,11662,11735,12580,12641,12703,12767,12829,12890,12958,13058,13118,13184,13257,13326,13383,13435,13497,14022,14098,14163,16694,16729,17035,17098,17153,17211,17269,17330,17393,17450,17501,17551,17612,17669,17735,17769,17804,17839,18444,20827,20899,20968,21037,21111,21183,21271,25036,115768,115969,116079,116280,116409,127807,127874,148911,149212,152872,153553,154235,154402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\30cced2c32333f1dda59fb97c4ecb3e2\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "263,283", "startColumns": "4,4", "startOffsets": "16897,17919", "endColumns": "41,59", "endOffsets": "16934,17974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9fce76da01519a7e775994f3de797a74\\transformed\\appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2030,2046,2052,2975,2991", "startColumns": "4,4,4,4,4", "startOffsets": "131919,132344,132522,163016,163427", "endLines": "2045,2051,2061,2990,2994", "endColumns": "24,24,24,24,24", "endOffsets": "132339,132517,132801,163422,163549"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Camera\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "320", "startColumns": "4", "startOffsets": "20398", "endColumns": "43", "endOffsets": "20437"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Camera\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "83", "endOffsets": "135"}, "to": {"startLines": "1806", "startColumns": "4", "startOffsets": "118998", "endColumns": "82", "endOffsets": "119076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f9b2f3ac4e57c2a361a436e16b1cb4e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "284", "startColumns": "4", "startOffsets": "17979", "endColumns": "53", "endOffsets": "18028"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\Camera\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "34,77,78,79,90,91,94", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1962,4849,4896,4943,5687,5732,5898", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1999,4891,4938,4985,5727,5772,5935"}}]}]}