package com.example.cameraxapp

import android.content.Context
import android.util.Size
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.segmentation.SegmentationMask
import com.google.mlkit.vision.segmentation.selfie.SelfieSegmenter
import com.google.mlkit.vision.segmentation.selfie.SelfieSegmenterOptions
import java.util.concurrent.Executor

typealias DepthMapCallback = (depthMap: FloatArray) -> Unit

class DepthAnalyzer(private val callback: DepthMapCallback) : ImageAnalysis.Analyzer {

    // Configure ML Kit Selfie Segmenter for depth
    private val segmenter = SelfieSegmenter.getClient(
        SelfieSegmenterOptions.Builder()
            .setDetectorMode(SelfieSegmenterOptions.STREAM_MODE)
            .enableDepth(true) // requires beta version: segmentation-selfie:16.0.0-beta4
            .build()
    )

    override fun analyze(image: ImageProxy) {
        val mediaImage = image.image ?: run {
            image.close()
            return
        }
        val inputImage = com.google.mlkit.vision.common.InputImage.fromMediaImage(mediaImage, image.imageInfo.rotationDegrees)

        segmenter.process(inputImage)
            .addOnSuccessListener { segmentationMask ->
                processDepthMap(segmentationMask)
                image.close()
            }
            .addOnFailureListener {
                image.close()
            }
    }

    private fun processDepthMap(mask: SegmentationMask) {
        // mask.buffer contains depth data as float32
        val depthData = FloatArray(mask.buffer.capacity() / 4)
        mask.buffer.asFloatBuffer().get(depthData)
        callback(depthData)
    }
}

fun createCinematicVideoAnalyzer(
    executor: Executor,
    onDepthReceived: (depthMap: FloatArray) -> Unit
): ImageAnalysis {
    return ImageAnalysis.Builder()
        .setTargetResolution(Size(1080, 1920))
        .build()
        .also { analysis ->
            analysis.setAnalyzer(executor, DepthAnalyzer { depthMap ->
                onDepthReceived(depthMap)
            })
        }
}