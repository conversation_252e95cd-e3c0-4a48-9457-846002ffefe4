{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,886,977,1069,1164,1258,1359,1452,1547,1641,1732,1823,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,77,90,91,94,93,100,92,94,93,90,90,78,105,100,96,108,99,109,159,102,79", "endOffsets": "207,311,418,505,605,725,803,881,972,1064,1159,1253,1354,1447,1542,1636,1727,1818,1897,2003,2104,2201,2310,2410,2520,2680,2783,2863"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,886,977,1069,1164,1258,1359,1452,1547,1641,1732,1823,1902,2008,2109,2206,2315,2415,2525,2685,8944", "endColumns": "106,103,106,86,99,119,77,77,90,91,94,93,100,92,94,93,90,90,78,105,100,96,108,99,109,159,102,79", "endOffsets": "207,311,418,505,605,725,803,881,972,1064,1159,1253,1354,1447,1542,1636,1727,1818,1897,2003,2104,2201,2310,2410,2520,2680,2783,9019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,986,1055,1138,1221,1296,1372,1438", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,981,1050,1133,1216,1291,1367,1433,1549"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3953,4045,7766,7859,8035,8200,8286,8387,8474,8560,8628,8697,8780,9024,9377,9453,9519", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "4040,4122,7854,7953,8117,8281,8382,8469,8555,8623,8692,8775,8858,9094,9448,9514,9630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3235,3329,3432,3529,3631,3733,3831,9182", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3324,3427,3524,3626,3728,3826,3948,9278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,394,502,581,676,789,924,1040,1178,1259,1359,1449,1545,1655,1779,1884,2015,2143,2269,2444,2566,2684,2807,2939,3030,3122,3245,3371,3468,3569,3672,3802,3939,4044,4141,4218,4296,4377,4460,4554,4630,4710,4807,4906,5000,5096,5179,5281,5376,5474,5588,5664,5760", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "160,268,389,497,576,671,784,919,1035,1173,1254,1354,1444,1540,1650,1774,1879,2010,2138,2264,2439,2561,2679,2802,2934,3025,3117,3240,3366,3463,3564,3667,3797,3934,4039,4136,4213,4291,4372,4455,4549,4625,4705,4802,4901,4995,5091,5174,5276,5371,5469,5583,5659,5755,5845"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2788,2898,3006,3127,4127,4206,4301,4414,4549,4665,4803,4884,4984,5074,5170,5280,5404,5509,5640,5768,5894,6069,6191,6309,6432,6564,6655,6747,6870,6996,7093,7194,7297,7427,7564,7669,7958,8122,8863,9099,9283,9635,9711,9791,9888,9987,10081,10177,10260,10362,10457,10555,10669,10745,10841", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "2893,3001,3122,3230,4201,4296,4409,4544,4660,4798,4879,4979,5069,5165,5275,5399,5504,5635,5763,5889,6064,6186,6304,6427,6559,6650,6742,6865,6991,7088,7189,7292,7422,7559,7664,7761,8030,8195,8939,9177,9372,9706,9786,9883,9982,10076,10172,10255,10357,10452,10550,10664,10740,10836,10926"}}]}]}