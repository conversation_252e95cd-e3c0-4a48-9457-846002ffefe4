package com.example.iphonesample

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.camera.view.PreviewView
import androidx.camera.core.*

class MainActivity : ComponentActivity() {

    private val cameraPermission = Manifest.permission.CAMERA

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Request camera permission
        val requestPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted ->
            // Handle permission result
        }

        if (ContextCompat.checkSelfPermission(this, cameraPermission) != PackageManager.PERMISSION_GRANTED) {
            requestPermissionLauncher.launch(cameraPermission)
        }

        setContent {
            MaterialTheme {
                CameraApp()
            }
        }
    }
}

@Composable
fun CameraApp() {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    // Zoom state
    var zoom by remember { mutableStateOf(1f) }

    Box(modifier = Modifier.fillMaxSize()) {
        CameraPreviewView(
            modifier = Modifier.fillMaxSize(),
            zoom = zoom,
            lifecycleOwner = lifecycleOwner,
            context = context
        )

        // Shutter Button example (bottom center)
        Button(
            onClick = { /* TODO: Capture Photo */ },
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 32.dp)
                .size(64.dp),
            shape = MaterialTheme.shapes.large,
            contentPadding = PaddingValues(0.dp)
        ) {
            Text("●", style = MaterialTheme.typography.headlineLarge)
        }

        // Zoom slider (bottom left)
        Column(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(start = 16.dp, bottom = 40.dp)
                .width(120.dp)
        ) {
            Text("Zoom", style = MaterialTheme.typography.bodyMedium)
            Slider(
                value = zoom,
                onValueChange = { zoom = it.coerceIn(1f, 5f) },
                valueRange = 1f..5f
            )
        }
    }
}

@Composable
fun CameraPreviewView(
    modifier: Modifier = Modifier,
    zoom: Float,
    lifecycleOwner: LifecycleOwner,
    context: android.content.Context
) {
    // Prepare camera provider and preview view
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }
    val previewView = remember { PreviewView(context) }

    // Remember camera instance to set zoom on changes
    var camera by remember { mutableStateOf<Camera?>(null) }

    LaunchedEffect(cameraProviderFuture) {
        val cameraProvider = cameraProviderFuture.get()
        val preview = Preview.Builder()
            .build()
            .also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }

        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

        cameraProvider.unbindAll()
        camera = cameraProvider.bindToLifecycle(
            lifecycleOwner,
            cameraSelector,
            preview
        )
    }

    // Update zoom when changed
    LaunchedEffect(zoom) {
        camera?.cameraControl?.setZoomRatio(zoom)
    }

    AndroidView(factory = { previewView }, modifier = modifier)
}