  Manifest android  CAMERA android.Manifest.permission  READ_MEDIA_IMAGES android.Manifest.permission  READ_MEDIA_VIDEO android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  Activity android.app  	CameraApp android.app.Activity  CameraController android.app.Activity  CameraTheme android.app.Activity  Suppress android.app.Activity  VIBRATOR_MANAGER_SERVICE android.app.Activity  VIBRATOR_SERVICE android.app.Activity  Vibrator android.app.Activity  VibratorManager android.app.Activity  android android.app.Activity  getSystemService android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  
setContent android.app.Activity  Context android.content  CAMERA_SERVICE android.content.Context  	CameraApp android.content.Context  CameraController android.content.Context  CameraTheme android.content.Context  Suppress android.content.Context  VIBRATOR_MANAGER_SERVICE android.content.Context  VIBRATOR_SERVICE android.content.Context  Vibrator android.content.Context  VibratorManager android.content.Context  android android.content.Context  getExternalFilesDir android.content.Context  getSystemService android.content.Context  	resources android.content.Context  
setContent android.content.Context  	CameraApp android.content.ContextWrapper  CameraController android.content.ContextWrapper  CameraTheme android.content.ContextWrapper  Suppress android.content.ContextWrapper  VIBRATOR_MANAGER_SERVICE android.content.ContextWrapper  VIBRATOR_SERVICE android.content.ContextWrapper  Vibrator android.content.ContextWrapper  VibratorManager android.content.ContextWrapper  android android.content.ContextWrapper  
setContent android.content.ContextWrapper  PackageManager android.content.pm  Bitmap android.graphics  
BitmapFactory android.graphics  Canvas android.graphics  Color android.graphics  ColorMatrix android.graphics  ColorMatrixColorFilter android.graphics  Context android.graphics  	Exception android.graphics  FilterProcessor android.graphics  
FilterType android.graphics  Float android.graphics  ImageFormat android.graphics  Int android.graphics  IntArray android.graphics  Log android.graphics  Matrix android.graphics  Paint android.graphics  
PorterDuff android.graphics  PorterDuffXfermode android.graphics  RadialGradient android.graphics  Shader android.graphics  android android.graphics  apply android.graphics  applyColorMatrix android.graphics  applyFilterOptimized android.graphics  coerceIn android.graphics  copyOf android.graphics  floatArrayOf android.graphics  
intArrayOf android.graphics  kotlin android.graphics  listOf android.graphics  
plusAssign android.graphics  resume android.graphics  suspendCancellableCoroutine android.graphics  until android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  compress android.graphics.Bitmap  config android.graphics.Bitmap  copy android.graphics.Bitmap  createBitmap android.graphics.Bitmap  	getPixels android.graphics.Bitmap  height android.graphics.Bitmap  recycle android.graphics.Bitmap  	setPixels android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  
decodeFile android.graphics.BitmapFactory  decodeResource android.graphics.BitmapFactory  
drawBitmap android.graphics.Canvas  	drawPoint android.graphics.Canvas  drawRect android.graphics.Canvas  TRANSPARENT android.graphics.Color  WHITE android.graphics.Color  argb android.graphics.Color  blue android.graphics.Color  green android.graphics.Color  red android.graphics.Color  ColorMatrix android.graphics.ColorMatrix  apply android.graphics.ColorMatrix  floatArrayOf android.graphics.ColorMatrix  
postConcat android.graphics.ColorMatrix  set android.graphics.ColorMatrix  
setSaturation android.graphics.ColorMatrix  JPEG android.graphics.ImageFormat  ColorMatrixColorFilter android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  colorFilter android.graphics.Paint  shader android.graphics.Paint  xfermode android.graphics.Paint  Mode android.graphics.PorterDuff  DST_OUT  android.graphics.PorterDuff.Mode  DST_OVER  android.graphics.PorterDuff.Mode  TileMode android.graphics.Shader  CLAMP  android.graphics.Shader.TileMode  util android.graphics.android  Size android.graphics.android.util  CameraCharacteristics android.hardware.camera2  
CameraManager android.hardware.camera2  CameraMetadata android.hardware.camera2  CONTROL_AE_COMPENSATION_RANGE .android.hardware.camera2.CameraCharacteristics  Key .android.hardware.camera2.CameraCharacteristics  )LENS_INFO_AVAILABLE_OPTICAL_STABILIZATION .android.hardware.camera2.CameraCharacteristics  SCALER_STREAM_CONFIGURATION_MAP .android.hardware.camera2.CameraCharacteristics  SENSOR_INFO_SENSITIVITY_RANGE .android.hardware.camera2.CameraCharacteristics  get .android.hardware.camera2.CameraCharacteristics  getCameraCharacteristics &android.hardware.camera2.CameraManager  "LENS_OPTICAL_STABILIZATION_MODE_ON 'android.hardware.camera2.CameraMetadata  StreamConfigurationMap android.hardware.camera2.params  getOutputSizes 6android.hardware.camera2.params.StreamConfigurationMap  Image 
android.media  scanFile $android.media.MediaScannerConnection  Uri android.net  fromFile android.net.Uri  GLES20 android.opengl  GLUtils android.opengl  	GL_LINEAR android.opengl.GLES20  GL_TEXTURE1 android.opengl.GLES20  
GL_TEXTURE_2D android.opengl.GLES20  GL_TEXTURE_MAG_FILTER android.opengl.GLES20  GL_TEXTURE_MIN_FILTER android.opengl.GLES20  glActiveTexture android.opengl.GLES20  
glBindTexture android.opengl.GLES20  
glGenTextures android.opengl.GLES20  glTexParameteri android.opengl.GLES20  
texImage2D android.opengl.GLUtils  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  VibrationEffect 
android.os  Vibrator 
android.os  VibratorManager 
android.os  BRAND android.os.Build  MANUFACTURER android.os.Build  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  DIRECTORY_MOVIES android.os.Environment  DIRECTORY_PICTURES android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  postDelayed android.os.Handler  
getMainLooper android.os.Looper  
createOneShot android.os.VibrationEffect  vibrate android.os.Vibrator  defaultVibrator android.os.VibratorManager  Log android.util  Range android.util  Size android.util  d android.util.Log  e android.util.Log  w android.util.Log  let android.util.Range  lower android.util.Range  upper android.util.Range  height android.util.Size  width android.util.Size  	CameraApp  android.view.ContextThemeWrapper  CameraController  android.view.ContextThemeWrapper  CameraTheme  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  VIBRATOR_MANAGER_SERVICE  android.view.ContextThemeWrapper  VIBRATOR_SERVICE  android.view.ContextThemeWrapper  Vibrator  android.view.ContextThemeWrapper  VibratorManager  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  post android.view.View  ComponentActivity androidx.activity  	CameraApp #androidx.activity.ComponentActivity  CameraController #androidx.activity.ComponentActivity  CameraTheme #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  VIBRATOR_MANAGER_SERVICE #androidx.activity.ComponentActivity  VIBRATOR_SERVICE #androidx.activity.ComponentActivity  Vibrator #androidx.activity.ComponentActivity  VibratorManager #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  RawRes androidx.annotation  Camera2CameraInfo androidx.camera.camera2.interop  cameraId 1androidx.camera.camera2.interop.Camera2CameraInfo  from 1androidx.camera.camera2.interop.Camera2CameraInfo  Any androidx.camera.core  Bitmap androidx.camera.core  
BitmapFactory androidx.camera.core  Boolean androidx.camera.core  Camera androidx.camera.core  Camera2CameraInfo androidx.camera.core  CameraCharacteristics androidx.camera.core  
CameraControl androidx.camera.core  CameraController androidx.camera.core  CameraFilter androidx.camera.core  
CameraInfo androidx.camera.core  
CameraManager androidx.camera.core  CameraMetadata androidx.camera.core  
CameraMode androidx.camera.core  CameraSelector androidx.camera.core  
CameraType androidx.camera.core  ClosedFloatingPointRange androidx.camera.core  ConcurrentCamera androidx.camera.core  Context androidx.camera.core  
ContextCompat androidx.camera.core  Date androidx.camera.core  Dispatchers androidx.camera.core  Environment androidx.camera.core  	Exception androidx.camera.core  ExecutorService androidx.camera.core  	Executors androidx.camera.core  FallbackStrategy androidx.camera.core  File androidx.camera.core  FileOutputOptions androidx.camera.core  FileOutputStream androidx.camera.core  FilterProcessor androidx.camera.core  
FilterType androidx.camera.core  	FlashMode androidx.camera.core  Float androidx.camera.core  FocusMeteringAction androidx.camera.core  GlyphController androidx.camera.core  
ImageAnalysis androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  
ImageProxy androidx.camera.core  IntRange androidx.camera.core  LifecycleOwner androidx.camera.core  LightCondition androidx.camera.core  List androidx.camera.core  Locale androidx.camera.core  Log androidx.camera.core  Map androidx.camera.core  
MeteringPoint androidx.camera.core  NothingPhoneOptimizations androidx.camera.core  PortraitProcessor androidx.camera.core  Preview androidx.camera.core  ProcessCameraProvider androidx.camera.core  Quality androidx.camera.core  QualitySelector androidx.camera.core  Recorder androidx.camera.core  	Recording androidx.camera.core  SimpleDateFormat androidx.camera.core  Size androidx.camera.core  String androidx.camera.core  #SurfaceOrientedMeteringPointFactory androidx.camera.core  Uri androidx.camera.core  VideoCapture androidx.camera.core  VideoController androidx.camera.core  VideoQuality androidx.camera.core  VideoRecordEvent androidx.camera.core  
ZoomRanges androidx.camera.core  	ZoomState androidx.camera.core  android androidx.camera.core  androidx androidx.camera.core  apply androidx.camera.core  applyFilterOptimized androidx.camera.core  applyFilterToSavedImage androidx.camera.core  applyNightModeFilter androidx.camera.core  applyPortraitProcessing androidx.camera.core  arrayOf androidx.camera.core  cameraExecutor androidx.camera.core  cameraProvider androidx.camera.core  captureNightModePhoto androidx.camera.core  capturePortraitPhoto androidx.camera.core  coerceIn androidx.camera.core  contains androidx.camera.core  context androidx.camera.core  createImageFile androidx.camera.core  
currentFilter androidx.camera.core  currentMode androidx.camera.core  delay androidx.camera.core  filter androidx.camera.core  find androidx.camera.core  	flashMode androidx.camera.core  glyphController androidx.camera.core  isNightModeEnabled androidx.camera.core  let androidx.camera.core  listOf androidx.camera.core  mapOf androidx.camera.core  rangeTo androidx.camera.core  repeat androidx.camera.core  setupCamera androidx.camera.core  to androidx.camera.core  use androidx.camera.core  withContext androidx.camera.core  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  let "androidx.camera.core.CameraControl  setZoomRatio "androidx.camera.core.CameraControl  startFocusAndMetering "androidx.camera.core.CameraControl  <SAM-CONSTRUCTOR> !androidx.camera.core.CameraFilter  	zoomState androidx.camera.core.CameraInfo  Builder #androidx.camera.core.CameraSelector  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  LENS_FACING_BACK #androidx.camera.core.CameraSelector  addCameraFilter +androidx.camera.core.CameraSelector.Builder  build +androidx.camera.core.CameraSelector.Builder  requireLensFacing +androidx.camera.core.CameraSelector.Builder  Builder (androidx.camera.core.FocusMeteringAction  build 0androidx.camera.core.FocusMeteringAction.Builder  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  setTargetResolution *androidx.camera.core.ImageAnalysis.Builder  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MAXIMIZE_QUALITY !androidx.camera.core.ImageCapture  FLASH_MODE_AUTO !androidx.camera.core.ImageCapture  FLASH_MODE_OFF !androidx.camera.core.ImageCapture  
FLASH_MODE_ON !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  	flashMode !androidx.camera.core.ImageCapture  let !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  setFlashMode )androidx.camera.core.ImageCapture.Builder  setJpegQuality )androidx.camera.core.ImageCapture.Builder  setTargetResolution )androidx.camera.core.ImageCapture.Builder  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  close androidx.camera.core.ImageProxy  height androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  width androidx.camera.core.ImageProxy  createPoint )androidx.camera.core.MeteringPointFactory  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  setTargetResolution $androidx.camera.core.Preview.Builder  createPoint 8androidx.camera.core.SurfaceOrientedMeteringPointFactory  
CameraType $androidx.camera.core.VideoController  Finalize %androidx.camera.core.VideoRecordEvent  Start %androidx.camera.core.VideoRecordEvent  	zoomRatio androidx.camera.core.ZoomState  camera androidx.camera.core.androidx  video $androidx.camera.core.androidx.camera  Recorder *androidx.camera.core.androidx.camera.video  VideoCapture *androidx.camera.core.androidx.camera.video  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  Any androidx.camera.video  Bitmap androidx.camera.video  
BitmapFactory androidx.camera.video  Boolean androidx.camera.video  Camera androidx.camera.video  CameraController androidx.camera.video  
CameraMode androidx.camera.video  CameraSelector androidx.camera.video  
CameraType androidx.camera.video  Context androidx.camera.video  
ContextCompat androidx.camera.video  Date androidx.camera.video  Dispatchers androidx.camera.video  Environment androidx.camera.video  	Exception androidx.camera.video  ExecutorService androidx.camera.video  	Executors androidx.camera.video  FallbackStrategy androidx.camera.video  File androidx.camera.video  FileOutputOptions androidx.camera.video  FileOutputStream androidx.camera.video  FilterProcessor androidx.camera.video  
FilterType androidx.camera.video  	FlashMode androidx.camera.video  Float androidx.camera.video  GlyphController androidx.camera.video  
ImageAnalysis androidx.camera.video  ImageCapture androidx.camera.video  ImageCaptureException androidx.camera.video  LifecycleOwner androidx.camera.video  List androidx.camera.video  Locale androidx.camera.video  Log androidx.camera.video  Map androidx.camera.video  NothingPhoneOptimizations androidx.camera.video  
OutputResults androidx.camera.video  PendingRecording androidx.camera.video  PortraitProcessor androidx.camera.video  Preview androidx.camera.video  ProcessCameraProvider androidx.camera.video  Quality androidx.camera.video  QualitySelector androidx.camera.video  Recorder androidx.camera.video  	Recording androidx.camera.video  SimpleDateFormat androidx.camera.video  Size androidx.camera.video  String androidx.camera.video  Uri androidx.camera.video  VideoCapture androidx.camera.video  VideoController androidx.camera.video  VideoQuality androidx.camera.video  VideoRecordEvent androidx.camera.video  android androidx.camera.video  androidx androidx.camera.video  apply androidx.camera.video  applyFilterOptimized androidx.camera.video  applyFilterToSavedImage androidx.camera.video  applyNightModeFilter androidx.camera.video  applyPortraitProcessing androidx.camera.video  arrayOf androidx.camera.video  cameraExecutor androidx.camera.video  cameraProvider androidx.camera.video  captureNightModePhoto androidx.camera.video  capturePortraitPhoto androidx.camera.video  coerceIn androidx.camera.video  context androidx.camera.video  createImageFile androidx.camera.video  
currentFilter androidx.camera.video  currentMode androidx.camera.video  delay androidx.camera.video  filter androidx.camera.video  	flashMode androidx.camera.video  glyphController androidx.camera.video  isNightModeEnabled androidx.camera.video  let androidx.camera.video  listOf androidx.camera.video  mapOf androidx.camera.video  repeat androidx.camera.video  setupCamera androidx.camera.video  to androidx.camera.video  use androidx.camera.video  withContext androidx.camera.video  lowerQualityOrHigherThan &androidx.camera.video.FallbackStrategy  Builder 'androidx.camera.video.FileOutputOptions  build /androidx.camera.video.FileOutputOptions.Builder  OnImageSavedCallback "androidx.camera.video.ImageCapture  OutputFileResults "androidx.camera.video.ImageCapture  	outputUri #androidx.camera.video.OutputResults  apply &androidx.camera.video.PendingRecording  start &androidx.camera.video.PendingRecording  withAudioEnabled &androidx.camera.video.PendingRecording  SurfaceProvider androidx.camera.video.Preview  FHD androidx.camera.video.Quality  HD androidx.camera.video.Quality  UHD androidx.camera.video.Quality  from %androidx.camera.video.QualitySelector  Builder androidx.camera.video.Recorder  prepareRecording androidx.camera.video.Recorder  build &androidx.camera.video.Recorder.Builder  setQualitySelector &androidx.camera.video.Recorder.Builder  stop androidx.camera.video.Recording  output "androidx.camera.video.VideoCapture  
withOutput "androidx.camera.video.VideoCapture  
CameraType %androidx.camera.video.VideoController  Finalize &androidx.camera.video.VideoRecordEvent  Start &androidx.camera.video.VideoRecordEvent  error /androidx.camera.video.VideoRecordEvent.Finalize  hasError /androidx.camera.video.VideoRecordEvent.Finalize  
outputResults /androidx.camera.video.VideoRecordEvent.Finalize  PreviewView androidx.camera.view  ImplementationMode  androidx.camera.view.PreviewView  PreviewView  androidx.camera.view.PreviewView  	ScaleType  androidx.camera.view.PreviewView  apply  androidx.camera.view.PreviewView  implementationMode  androidx.camera.view.PreviewView  post  androidx.camera.view.PreviewView  	scaleType  androidx.camera.view.PreviewView  surfaceProvider  androidx.camera.view.PreviewView  
COMPATIBLE 3androidx.camera.view.PreviewView.ImplementationMode  FILL_CENTER *androidx.camera.view.PreviewView.ScaleType  	Alignment androidx.compose.animation.core  Arrangement androidx.compose.animation.core  Boolean androidx.compose.animation.core  Box androidx.compose.animation.core  CameraController androidx.compose.animation.core  
CameraMode androidx.compose.animation.core  CameraPreviewView androidx.compose.animation.core  Card androidx.compose.animation.core  CardDefaults androidx.compose.animation.core  CircleShape androidx.compose.animation.core  Color androidx.compose.animation.core  Column androidx.compose.animation.core  
Composable androidx.compose.animation.core  FilterButton androidx.compose.animation.core  
FilterType androidx.compose.animation.core  FlashButton androidx.compose.animation.core  	FlashMode androidx.compose.animation.core  Float androidx.compose.animation.core  
IconButton androidx.compose.animation.core  ImageThumbnailPreview androidx.compose.animation.core  Int androidx.compose.animation.core  LaunchedEffect androidx.compose.animation.core  List androidx.compose.animation.core  MacroModeToggle androidx.compose.animation.core  
MaterialTheme androidx.compose.animation.core  ModeSelector androidx.compose.animation.core  Modifier androidx.compose.animation.core  PortraitModeIndicator androidx.compose.animation.core  PreviewView androidx.compose.animation.core  RoundedCornerShape androidx.compose.animation.core  Row androidx.compose.animation.core  
ShutterButton androidx.compose.animation.core  Spacer androidx.compose.animation.core  String androidx.compose.animation.core  Text androidx.compose.animation.core  
TopControlBar androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  Unit androidx.compose.animation.core  VibrationEffect androidx.compose.animation.core  Vibrator androidx.compose.animation.core  VideoQualitySelector androidx.compose.animation.core  VideoRecordingTimer androidx.compose.animation.core  ZoomControls androidx.compose.animation.core  
ZoomIndicator androidx.compose.animation.core  align androidx.compose.animation.core  android androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  apply androidx.compose.animation.core  
background androidx.compose.animation.core  
cardColors androidx.compose.animation.core  	clickable androidx.compose.animation.core  
coerceAtLeast androidx.compose.animation.core  coerceAtMost androidx.compose.animation.core  coerceIn androidx.compose.animation.core  delay androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  fillMaxWidth androidx.compose.animation.core  forEach androidx.compose.animation.core  forEachIndexed androidx.compose.animation.core  format androidx.compose.animation.core  formatRecordingTime androidx.compose.animation.core  getValue androidx.compose.animation.core  kotlin androidx.compose.animation.core  	lastIndex androidx.compose.animation.core  launch androidx.compose.animation.core  listOf androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  offset androidx.compose.animation.core  padding androidx.compose.animation.core  pointerInput androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  remember androidx.compose.animation.core  rememberCoroutineScope androidx.compose.animation.core  setValue androidx.compose.animation.core  size androidx.compose.animation.core  spacedBy androidx.compose.animation.core  split androidx.compose.animation.core  
startsWith androidx.compose.animation.core  tween androidx.compose.animation.core  width androidx.compose.animation.core  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  detectHorizontalDragGestures $androidx.compose.foundation.gestures  detectTapGestures $androidx.compose.foundation.gestures  detectTransformGestures $androidx.compose.foundation.gestures  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  	CameraApp "androidx.compose.foundation.layout  CameraController "androidx.compose.foundation.layout  
CameraMode "androidx.compose.foundation.layout  CameraPreviewView "androidx.compose.foundation.layout  CameraTheme "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalPermissionsApi "androidx.compose.foundation.layout  FilterButton "androidx.compose.foundation.layout  
FilterType "androidx.compose.foundation.layout  FlashButton "androidx.compose.foundation.layout  	FlashMode "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  ImageThumbnailPreview "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  MacroModeToggle "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  ModeSelector "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  PortraitModeIndicator "androidx.compose.foundation.layout  PreviewView "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  
ShutterButton "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Suppress "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TopControlBar "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  VIBRATOR_MANAGER_SERVICE "androidx.compose.foundation.layout  VIBRATOR_SERVICE "androidx.compose.foundation.layout  VibrationEffect "androidx.compose.foundation.layout  Vibrator "androidx.compose.foundation.layout  VibratorManager "androidx.compose.foundation.layout  VideoQualitySelector "androidx.compose.foundation.layout  VideoRecordingTimer "androidx.compose.foundation.layout  ZoomControls "androidx.compose.foundation.layout  
ZoomIndicator "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  animateFloatAsState "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  
coerceAtLeast "androidx.compose.foundation.layout  coerceAtMost "androidx.compose.foundation.layout  coerceIn "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  formatRecordingTime "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  kotlin "androidx.compose.foundation.layout  	lastIndex "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  pointerInput "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  split "androidx.compose.foundation.layout  
startsWith "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  CameraPreviewView +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FilterType +androidx.compose.foundation.layout.BoxScope  ImageThumbnailPreview +androidx.compose.foundation.layout.BoxScope  MacroModeToggle +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  ModeSelector +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  PortraitModeIndicator +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  
ShutterButton +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  
TopControlBar +androidx.compose.foundation.layout.BoxScope  Unit +androidx.compose.foundation.layout.BoxScope  VibrationEffect +androidx.compose.foundation.layout.BoxScope  VideoQualitySelector +androidx.compose.foundation.layout.BoxScope  VideoRecordingTimer +androidx.compose.foundation.layout.BoxScope  ZoomControls +androidx.compose.foundation.layout.BoxScope  
ZoomIndicator +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  detectTapGestures +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  forEach +androidx.compose.foundation.layout.BoxScope  launch +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  pointerInput +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  split +androidx.compose.foundation.layout.BoxScope  
startsWith +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FilterType .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  animateFloatAsState .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  formatRecordingTime .androidx.compose.foundation.layout.ColumnScope  getValue .androidx.compose.foundation.layout.ColumnScope  kotlin .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  provideDelegate .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  split .androidx.compose.foundation.layout.ColumnScope  
startsWith .androidx.compose.foundation.layout.ColumnScope  tween .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Box +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  FilterButton +androidx.compose.foundation.layout.RowScope  
FilterType +androidx.compose.foundation.layout.RowScope  FlashButton +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  animateFloatAsState +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  
cardColors +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  formatRecordingTime +androidx.compose.foundation.layout.RowScope  getValue +androidx.compose.foundation.layout.RowScope  kotlin +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  provideDelegate +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  tween +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  	CameraApp androidx.compose.material3  CameraController androidx.compose.material3  
CameraMode androidx.compose.material3  CameraPreviewView androidx.compose.material3  CameraTheme androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalPermissionsApi androidx.compose.material3  FilterButton androidx.compose.material3  
FilterType androidx.compose.material3  FlashButton androidx.compose.material3  	FlashMode androidx.compose.material3  Float androidx.compose.material3  
IconButton androidx.compose.material3  ImageThumbnailPreview androidx.compose.material3  Int androidx.compose.material3  LaunchedEffect androidx.compose.material3  List androidx.compose.material3  MacroModeToggle androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  ModeSelector androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  PortraitModeIndicator androidx.compose.material3  PreviewView androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  
ShutterButton androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Suppress androidx.compose.material3  Text androidx.compose.material3  
TopControlBar androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  VIBRATOR_MANAGER_SERVICE androidx.compose.material3  VIBRATOR_SERVICE androidx.compose.material3  VibrationEffect androidx.compose.material3  Vibrator androidx.compose.material3  VibratorManager androidx.compose.material3  VideoQualitySelector androidx.compose.material3  VideoRecordingTimer androidx.compose.material3  ZoomControls androidx.compose.material3  
ZoomIndicator androidx.compose.material3  align androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  animateFloatAsState androidx.compose.material3  apply androidx.compose.material3  
background androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  
coerceAtLeast androidx.compose.material3  coerceAtMost androidx.compose.material3  coerceIn androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  forEachIndexed androidx.compose.material3  format androidx.compose.material3  formatRecordingTime androidx.compose.material3  getValue androidx.compose.material3  kotlin androidx.compose.material3  	lastIndex androidx.compose.material3  launch androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  offset androidx.compose.material3  padding androidx.compose.material3  pointerInput androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  split androidx.compose.material3  
startsWith androidx.compose.material3  tween androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  	CameraApp androidx.compose.runtime  CameraController androidx.compose.runtime  
CameraMode androidx.compose.runtime  CameraPreviewView androidx.compose.runtime  CameraTheme androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalPermissionsApi androidx.compose.runtime  FilterButton androidx.compose.runtime  
FilterType androidx.compose.runtime  FlashButton androidx.compose.runtime  	FlashMode androidx.compose.runtime  Float androidx.compose.runtime  
IconButton androidx.compose.runtime  ImageThumbnailPreview androidx.compose.runtime  Int androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  List androidx.compose.runtime  MacroModeToggle androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  ModeSelector androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  PortraitModeIndicator androidx.compose.runtime  PreviewView androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  
ShutterButton androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Suppress androidx.compose.runtime  Text androidx.compose.runtime  
TopControlBar androidx.compose.runtime  Unit androidx.compose.runtime  VIBRATOR_MANAGER_SERVICE androidx.compose.runtime  VIBRATOR_SERVICE androidx.compose.runtime  VibrationEffect androidx.compose.runtime  Vibrator androidx.compose.runtime  VibratorManager androidx.compose.runtime  VideoQualitySelector androidx.compose.runtime  VideoRecordingTimer androidx.compose.runtime  ZoomControls androidx.compose.runtime  
ZoomIndicator androidx.compose.runtime  align androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  animateFloatAsState androidx.compose.runtime  apply androidx.compose.runtime  
background androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  
coerceAtLeast androidx.compose.runtime  coerceAtMost androidx.compose.runtime  coerceIn androidx.compose.runtime  delay androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  forEachIndexed androidx.compose.runtime  format androidx.compose.runtime  formatRecordingTime androidx.compose.runtime  getValue androidx.compose.runtime  kotlin androidx.compose.runtime  	lastIndex androidx.compose.runtime  launch androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  offset androidx.compose.runtime  padding androidx.compose.runtime  pointerInput androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  split androidx.compose.runtime  
startsWith androidx.compose.runtime  tween androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	BottomEnd androidx.compose.ui.Alignment  BottomStart androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  TopStart androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  	BottomEnd 'androidx.compose.ui.Alignment.Companion  BottomStart 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  TopStart 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  offset &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  consume 4androidx.compose.ui.input.pointer.PointerInputChange  VibrationEffect 3androidx.compose.ui.input.pointer.PointerInputScope  android 3androidx.compose.ui.input.pointer.PointerInputScope  
coerceAtLeast 3androidx.compose.ui.input.pointer.PointerInputScope  coerceAtMost 3androidx.compose.ui.input.pointer.PointerInputScope  coerceIn 3androidx.compose.ui.input.pointer.PointerInputScope  detectHorizontalDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  detectTapGestures 3androidx.compose.ui.input.pointer.PointerInputScope  detectTransformGestures 3androidx.compose.ui.input.pointer.PointerInputScope  kotlin 3androidx.compose.ui.input.pointer.PointerInputScope  	lastIndex 3androidx.compose.ui.input.pointer.PointerInputScope  launch 3androidx.compose.ui.input.pointer.PointerInputScope  size 3androidx.compose.ui.input.pointer.PointerInputScope  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  IntSize androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  times androidx.compose.ui.unit.Dp  height  androidx.compose.ui.unit.IntSize  width  androidx.compose.ui.unit.IntSize  AndroidView androidx.compose.ui.viewinterop  	CameraApp #androidx.core.app.ComponentActivity  CameraController #androidx.core.app.ComponentActivity  CameraTheme #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  VIBRATOR_MANAGER_SERVICE #androidx.core.app.ComponentActivity  VIBRATOR_SERVICE #androidx.core.app.ComponentActivity  Vibrator #androidx.core.app.ComponentActivity  VibratorManager #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  getMainExecutor #androidx.core.content.ContextCompat  Consumer androidx.core.util  <SAM-CONSTRUCTOR> androidx.core.util.Consumer  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  lifecycleScope androidx.lifecycle  value androidx.lifecycle.LiveData  	Alignment com.example.camera  Any com.example.camera  Arrangement com.example.camera  
AtomicBoolean com.example.camera  Bitmap com.example.camera  
BitmapFactory com.example.camera  Boolean com.example.camera  Box com.example.camera  Build com.example.camera  Bundle com.example.camera  Button com.example.camera  Camera com.example.camera  Camera2CameraInfo com.example.camera  	CameraApp com.example.camera  CameraCharacteristics com.example.camera  CameraController com.example.camera  
CameraManager com.example.camera  CameraMetadata com.example.camera  
CameraMode com.example.camera  CameraPreviewView com.example.camera  CameraSelector com.example.camera  CameraTheme com.example.camera  
CameraType com.example.camera  Canvas com.example.camera  Card com.example.camera  CardDefaults com.example.camera  CircleShape com.example.camera  ClosedFloatingPointRange com.example.camera  Color com.example.camera  ColorMatrix com.example.camera  ColorMatrixColorFilter com.example.camera  Column com.example.camera  ComponentActivity com.example.camera  
Composable com.example.camera  Context com.example.camera  
ContextCompat com.example.camera  CoroutineScope com.example.camera  Date com.example.camera  
DepthAnalyzer com.example.camera  DepthMapCallback com.example.camera  Dispatchers com.example.camera  Environment com.example.camera  	Exception com.example.camera  Executor com.example.camera  ExecutorService com.example.camera  	Executors com.example.camera  ExperimentalPermissionsApi com.example.camera  FallbackStrategy com.example.camera  File com.example.camera  FileOutputOptions com.example.camera  FileOutputStream com.example.camera  FilterButton com.example.camera  FilterProcessor com.example.camera  
FilterType com.example.camera  FlashButton com.example.camera  	FlashMode com.example.camera  Float com.example.camera  
FloatArray com.example.camera  FocusMeteringAction com.example.camera  GLES20 com.example.camera  GLUtils com.example.camera  GlyphController com.example.camera  
IconButton com.example.camera  
ImageAnalysis com.example.camera  ImageCapture com.example.camera  ImageCaptureException com.example.camera  
ImageProxy com.example.camera  ImageThumbnailPreview com.example.camera  Int com.example.camera  IntArray com.example.camera  IntRange com.example.camera  	LUTShader com.example.camera  LaunchedEffect com.example.camera  LifecycleOwner com.example.camera  LightCondition com.example.camera  List com.example.camera  Locale com.example.camera  Log com.example.camera  Long com.example.camera  MacroModeToggle com.example.camera  MainActivity com.example.camera  Manifest com.example.camera  Map com.example.camera  
MaterialTheme com.example.camera  ModeSelector com.example.camera  Modifier com.example.camera  NothingPhoneOptimizations com.example.camera  OptIn com.example.camera  Paint com.example.camera  PerformanceOptimizer com.example.camera  
PorterDuff com.example.camera  PorterDuffXfermode com.example.camera  PortraitModeIndicator com.example.camera  PortraitProcessor com.example.camera  Preview com.example.camera  PreviewView com.example.camera  ProcessCameraProvider com.example.camera  Quality com.example.camera  QualitySelector com.example.camera  RadialGradient com.example.camera  RawRes com.example.camera  Recorder com.example.camera  	Recording com.example.camera  RoundedCornerShape com.example.camera  Row com.example.camera  Runtime com.example.camera  Shader com.example.camera  
ShutterButton com.example.camera  SimpleDateFormat com.example.camera  Size com.example.camera  Spacer com.example.camera  String com.example.camera  
SupervisorJob com.example.camera  Suppress com.example.camera  System com.example.camera  Text com.example.camera  
TopControlBar com.example.camera  Unit com.example.camera  Uri com.example.camera  VIBRATOR_MANAGER_SERVICE com.example.camera  VIBRATOR_SERVICE com.example.camera  VibrationEffect com.example.camera  Vibrator com.example.camera  VibratorManager com.example.camera  VideoCapture com.example.camera  VideoController com.example.camera  VideoQuality com.example.camera  VideoQualitySelector com.example.camera  VideoRecordEvent com.example.camera  VideoRecordingTimer com.example.camera  ZoomControls com.example.camera  
ZoomIndicator com.example.camera  
ZoomRanges com.example.camera  align com.example.camera  also com.example.camera  android com.example.camera  androidx com.example.camera  animateFloatAsState com.example.camera  apply com.example.camera  applyColorMatrix com.example.camera  applyFilterOptimized com.example.camera  applyFilterToSavedImage com.example.camera  applyNightModeFilter com.example.camera  applyNothingPhoneOptimizations com.example.camera  applyPerformanceOptimizations com.example.camera  applyPortraitProcessing com.example.camera  arrayOf com.example.camera  
background com.example.camera  cameraExecutor com.example.camera  cameraProvider com.example.camera  cancel com.example.camera  captureNightModePhoto com.example.camera  capturePortraitPhoto com.example.camera  
cardColors com.example.camera  	clickable com.example.camera  
coerceAtLeast com.example.camera  coerceAtMost com.example.camera  coerceIn com.example.camera  contains com.example.camera  context com.example.camera  copyOf com.example.camera  createCinematicVideoAnalyzer com.example.camera  createImageFile com.example.camera  
currentFilter com.example.camera  currentMode com.example.camera  delay com.example.camera  enableGPUAcceleration com.example.camera  equals com.example.camera  fillMaxSize com.example.camera  fillMaxWidth com.example.camera  filter com.example.camera  find com.example.camera  	flashMode com.example.camera  floatArrayOf com.example.camera  forEach com.example.camera  forEachIndexed com.example.camera  format com.example.camera  formatRecordingTime com.example.camera  getValue com.example.camera  glyphController com.example.camera  iPhoneCameraUI com.example.camera  
intArrayOf com.example.camera  isNightModeEnabled com.example.camera  kotlin com.example.camera  	lastIndex com.example.camera  launch com.example.camera  let com.example.camera  listOf com.example.camera  mapOf com.example.camera  measureTimeMillis com.example.camera  monitorPerformance com.example.camera  mutableStateOf com.example.camera  offset com.example.camera  optimizeForSnapdragon778G com.example.camera  padding com.example.camera  
plusAssign com.example.camera  pointerInput com.example.camera  provideDelegate com.example.camera  rangeTo com.example.camera  remember com.example.camera  rememberCoroutineScope com.example.camera  repeat com.example.camera  resume com.example.camera  run com.example.camera  setValue com.example.camera  setupCamera com.example.camera  size com.example.camera  smoothVideoZoomTransition com.example.camera  spacedBy com.example.camera  split com.example.camera  
startsWith com.example.camera  suspendCancellableCoroutine com.example.camera  to com.example.camera  tween com.example.camera  until com.example.camera  use com.example.camera  width com.example.camera  withContext com.example.camera  Bitmap #com.example.camera.CameraController  
BitmapFactory #com.example.camera.CameraController  
CameraMode #com.example.camera.CameraController  CameraSelector #com.example.camera.CameraController  
ContextCompat #com.example.camera.CameraController  Date #com.example.camera.CameraController  Dispatchers #com.example.camera.CameraController  Environment #com.example.camera.CameraController  	Executors #com.example.camera.CameraController  File #com.example.camera.CameraController  FileOutputStream #com.example.camera.CameraController  FilterProcessor #com.example.camera.CameraController  
FilterType #com.example.camera.CameraController  	FlashMode #com.example.camera.CameraController  GlyphController #com.example.camera.CameraController  
ImageAnalysis #com.example.camera.CameraController  ImageCapture #com.example.camera.CameraController  Locale #com.example.camera.CameraController  Log #com.example.camera.CameraController  NothingPhoneOptimizations #com.example.camera.CameraController  PerformanceOptimizer #com.example.camera.CameraController  PortraitProcessor #com.example.camera.CameraController  ProcessCameraProvider #com.example.camera.CameraController  SimpleDateFormat #com.example.camera.CameraController  Size #com.example.camera.CameraController  Uri #com.example.camera.CameraController  VideoController #com.example.camera.CameraController  android #com.example.camera.CameraController  androidx #com.example.camera.CameraController  applyFilterOptimized #com.example.camera.CameraController  applyFilterToSavedImage #com.example.camera.CameraController  applyNightModeFilter #com.example.camera.CameraController  applyPortraitProcessing #com.example.camera.CameraController  arrayOf #com.example.camera.CameraController  bindCameraUseCases #com.example.camera.CameraController  camera #com.example.camera.CameraController  cameraExecutor #com.example.camera.CameraController  cameraProvider #com.example.camera.CameraController  captureNightModePhoto #com.example.camera.CameraController  capturePhoto #com.example.camera.CameraController  capturePortraitPhoto #com.example.camera.CameraController  cleanup #com.example.camera.CameraController  coerceIn #com.example.camera.CameraController  context #com.example.camera.CameraController  createImageFile #com.example.camera.CameraController  currentCameraSelector #com.example.camera.CameraController  
currentFilter #com.example.camera.CameraController  currentMode #com.example.camera.CameraController  currentZoom #com.example.camera.CameraController  currentZoomIndex #com.example.camera.CameraController  delay #com.example.camera.CameraController  enableContinuousAutoFocus #com.example.camera.CameraController  enableMacroMode #com.example.camera.CameraController  enableNightMode #com.example.camera.CameraController  	enableOIS #com.example.camera.CameraController  filter #com.example.camera.CameraController  filterProcessor #com.example.camera.CameraController  	flashMode #com.example.camera.CameraController  focusOnPoint #com.example.camera.CameraController  getAvailableZoomLevels #com.example.camera.CameraController  getCurrentMode #com.example.camera.CameraController  getUltraWideCameraSelector #com.example.camera.CameraController  glyphController #com.example.camera.CameraController  
imageAnalyzer #com.example.camera.CameraController  imageCapture #com.example.camera.CameraController  initializeCamera #com.example.camera.CameraController  isGlyphFlashEnabled #com.example.camera.CameraController  isMacroModeEnabled #com.example.camera.CameraController  isNightModeEnabled #com.example.camera.CameraController  let #com.example.camera.CameraController  lifecycleOwner #com.example.camera.CameraController  listOf #com.example.camera.CameraController  
optimizations #com.example.camera.CameraController  portraitProcessor #com.example.camera.CameraController  portraitZoomLevels #com.example.camera.CameraController  preview #com.example.camera.CameraController  repeat #com.example.camera.CameraController  	setFilter #com.example.camera.CameraController  setMode #com.example.camera.CameraController  setSurfaceProvider #com.example.camera.CameraController  setZoom #com.example.camera.CameraController  setupCamera #com.example.camera.CameraController  smoothZoomTo #com.example.camera.CameraController  startVideoRecording #com.example.camera.CameraController  stopVideoRecording #com.example.camera.CameraController  switchToUltraWide #com.example.camera.CameraController  toggleFlash #com.example.camera.CameraController  use #com.example.camera.CameraController  videoCapture #com.example.camera.CameraController  videoController #com.example.camera.CameraController  withContext #com.example.camera.CameraController  
zoomLevels #com.example.camera.CameraController  	CINEMATIC com.example.camera.CameraMode  PHOTO com.example.camera.CameraMode  PORTRAIT com.example.camera.CameraMode  VIDEO com.example.camera.CameraMode  
FloatArray  com.example.camera.DepthAnalyzer  callback  com.example.camera.DepthAnalyzer  run  com.example.camera.DepthAnalyzer  Bitmap "com.example.camera.FilterProcessor  Canvas "com.example.camera.FilterProcessor  ColorMatrix "com.example.camera.FilterProcessor  ColorMatrixColorFilter "com.example.camera.FilterProcessor  
FilterType "com.example.camera.FilterProcessor  Log "com.example.camera.FilterProcessor  Paint "com.example.camera.FilterProcessor  apply "com.example.camera.FilterProcessor  applyBrilliantFilter "com.example.camera.FilterProcessor  applyColorMatrix "com.example.camera.FilterProcessor  applyDramaticFilter "com.example.camera.FilterProcessor  applyFilter "com.example.camera.FilterProcessor  applyFilterOptimized "com.example.camera.FilterProcessor  applyMonoFilter "com.example.camera.FilterProcessor  applyNightModeEnhancement "com.example.camera.FilterProcessor  applyNoirFilter "com.example.camera.FilterProcessor  applySilvertoneFilter "com.example.camera.FilterProcessor  applyVividFilter "com.example.camera.FilterProcessor  cleanup "com.example.camera.FilterProcessor  floatArrayOf "com.example.camera.FilterProcessor  	BRILLIANT com.example.camera.FilterType  DRAMATIC com.example.camera.FilterType  MONO com.example.camera.FilterType  NOIR com.example.camera.FilterType  NONE com.example.camera.FilterType  
SILVERTONE com.example.camera.FilterType  VIVID com.example.camera.FilterType  values com.example.camera.FilterType  AUTO com.example.camera.FlashMode  GLYPH com.example.camera.FlashMode  OFF com.example.camera.FlashMode  ON com.example.camera.FlashMode  Build "com.example.camera.GlyphController  Log "com.example.camera.GlyphController  android "com.example.camera.GlyphController  coerceIn "com.example.camera.GlyphController  completeNightModeCapture "com.example.camera.GlyphController  disableGlyphLights "com.example.camera.GlyphController  enableGlyphLights "com.example.camera.GlyphController  equals "com.example.camera.GlyphController  isGlyphAvailable "com.example.camera.GlyphController  isNothingPhone "com.example.camera.GlyphController  
pulseForFocus "com.example.camera.GlyphController  setFlashMode "com.example.camera.GlyphController  setProgressRing "com.example.camera.GlyphController  startNightModeCapture "com.example.camera.GlyphController  startVideoRecording "com.example.camera.GlyphController  stopVideoRecording "com.example.camera.GlyphController  triggerCaptureEffect "com.example.camera.GlyphController  triggerCaptureFlash "com.example.camera.GlyphController  Analyzer  com.example.camera.ImageAnalysis  OnImageSavedCallback com.example.camera.ImageCapture  OutputFileResults com.example.camera.ImageCapture  
BitmapFactory com.example.camera.LUTShader  GLES20 com.example.camera.LUTShader  GLUtils com.example.camera.LUTShader  IntArray com.example.camera.LUTShader  context com.example.camera.LUTShader  loadLUTTexture com.example.camera.LUTShader  lutTextureId com.example.camera.LUTShader  	LOW_LIGHT !com.example.camera.LightCondition  NORMAL !com.example.camera.LightCondition  	CameraApp com.example.camera.MainActivity  CameraController com.example.camera.MainActivity  CameraTheme com.example.camera.MainActivity  VIBRATOR_MANAGER_SERVICE com.example.camera.MainActivity  VIBRATOR_SERVICE com.example.camera.MainActivity  android com.example.camera.MainActivity  cameraController com.example.camera.MainActivity  getSystemService com.example.camera.MainActivity  
setContent com.example.camera.MainActivity  vibrator com.example.camera.MainActivity  Camera2CameraInfo ,com.example.camera.NothingPhoneOptimizations  CameraCharacteristics ,com.example.camera.NothingPhoneOptimizations  CameraMetadata ,com.example.camera.NothingPhoneOptimizations  CameraSelector ,com.example.camera.NothingPhoneOptimizations  Context ,com.example.camera.NothingPhoneOptimizations  
FilterType ,com.example.camera.NothingPhoneOptimizations  ImageCapture ,com.example.camera.NothingPhoneOptimizations  IntRange ,com.example.camera.NothingPhoneOptimizations  LightCondition ,com.example.camera.NothingPhoneOptimizations  Log ,com.example.camera.NothingPhoneOptimizations  Preview ,com.example.camera.NothingPhoneOptimizations  Size ,com.example.camera.NothingPhoneOptimizations  
ZoomRanges ,com.example.camera.NothingPhoneOptimizations  android ,com.example.camera.NothingPhoneOptimizations  
cameraManager ,com.example.camera.NothingPhoneOptimizations  contains ,com.example.camera.NothingPhoneOptimizations  context ,com.example.camera.NothingPhoneOptimizations  detectLightConditions ,com.example.camera.NothingPhoneOptimizations  filter ,com.example.camera.NothingPhoneOptimizations  find ,com.example.camera.NothingPhoneOptimizations  getOptimalPhotoResolution ,com.example.camera.NothingPhoneOptimizations  getOptimalPreviewResolution ,com.example.camera.NothingPhoneOptimizations  getOptimizedImageCaptureConfig ,com.example.camera.NothingPhoneOptimizations  getOptimizedPreviewConfig ,com.example.camera.NothingPhoneOptimizations  getSnapdragon778GOptimizations ,com.example.camera.NothingPhoneOptimizations  let ,com.example.camera.NothingPhoneOptimizations  listOf ,com.example.camera.NothingPhoneOptimizations  mainCameraId ,com.example.camera.NothingPhoneOptimizations  mapOf ,com.example.camera.NothingPhoneOptimizations  rangeTo ,com.example.camera.NothingPhoneOptimizations  to ,com.example.camera.NothingPhoneOptimizations  ultraWideCameraId ,com.example.camera.NothingPhoneOptimizations  
AtomicBoolean 'com.example.camera.PerformanceOptimizer  Build 'com.example.camera.PerformanceOptimizer  CoroutineScope 'com.example.camera.PerformanceOptimizer  Dispatchers 'com.example.camera.PerformanceOptimizer  Log 'com.example.camera.PerformanceOptimizer  Runtime 'com.example.camera.PerformanceOptimizer  
SupervisorJob 'com.example.camera.PerformanceOptimizer  System 'com.example.camera.PerformanceOptimizer  cancel 'com.example.camera.PerformanceOptimizer  checkMemoryUsage 'com.example.camera.PerformanceOptimizer  context 'com.example.camera.PerformanceOptimizer  delay 'com.example.camera.PerformanceOptimizer  getOptimalCameraSettings 'com.example.camera.PerformanceOptimizer  getOptimalHeapSize 'com.example.camera.PerformanceOptimizer  isOptimizing 'com.example.camera.PerformanceOptimizer  launch 'com.example.camera.PerformanceOptimizer  listOf 'com.example.camera.PerformanceOptimizer  mapOf 'com.example.camera.PerformanceOptimizer  maxMemoryUsage 'com.example.camera.PerformanceOptimizer  measureTimeMillis 'com.example.camera.PerformanceOptimizer  monitorPerformance 'com.example.camera.PerformanceOptimizer  optimizeForNothingPhone 'com.example.camera.PerformanceOptimizer  optimizeForPortraitMode 'com.example.camera.PerformanceOptimizer  optimizeForVideoRecording 'com.example.camera.PerformanceOptimizer  optimizeFrameRate 'com.example.camera.PerformanceOptimizer  performanceScope 'com.example.camera.PerformanceOptimizer  stopPerformanceMonitoring 'com.example.camera.PerformanceOptimizer  targetFrameTime 'com.example.camera.PerformanceOptimizer  to 'com.example.camera.PerformanceOptimizer  triggerGarbageCollection 'com.example.camera.PerformanceOptimizer  Bitmap $com.example.camera.PortraitProcessor  Canvas $com.example.camera.PortraitProcessor  Color $com.example.camera.PortraitProcessor  ColorMatrix $com.example.camera.PortraitProcessor  FilterProcessor $com.example.camera.PortraitProcessor  
FilterType $com.example.camera.PortraitProcessor  IntArray $com.example.camera.PortraitProcessor  Log $com.example.camera.PortraitProcessor  Paint $com.example.camera.PortraitProcessor  
PorterDuff $com.example.camera.PortraitProcessor  PorterDuffXfermode $com.example.camera.PortraitProcessor  RadialGradient $com.example.camera.PortraitProcessor  Shader $com.example.camera.PortraitProcessor  android $com.example.camera.PortraitProcessor  applyBoxBlur $com.example.camera.PortraitProcessor  applyColorMatrix $com.example.camera.PortraitProcessor  applyFallbackBlur $com.example.camera.PortraitProcessor  applyFilterOptimized $com.example.camera.PortraitProcessor  cleanup $com.example.camera.PortraitProcessor  coerceIn $com.example.camera.PortraitProcessor  context $com.example.camera.PortraitProcessor  copyOf $com.example.camera.PortraitProcessor  createAdvancedDepthMask $com.example.camera.PortraitProcessor  createFallbackDepthMask $com.example.camera.PortraitProcessor  detectEdges $com.example.camera.PortraitProcessor  enhanceForZoomLevel $com.example.camera.PortraitProcessor  filterProcessor $com.example.camera.PortraitProcessor  floatArrayOf $com.example.camera.PortraitProcessor  getGray $com.example.camera.PortraitProcessor  getOptimalPortraitResolution $com.example.camera.PortraitProcessor  
intArrayOf $com.example.camera.PortraitProcessor  kotlin $com.example.camera.PortraitProcessor  listOf $com.example.camera.PortraitProcessor  
plusAssign $com.example.camera.PortraitProcessor  resume $com.example.camera.PortraitProcessor  suspendCancellableCoroutine $com.example.camera.PortraitProcessor  until $com.example.camera.PortraitProcessor  SurfaceProvider com.example.camera.Preview  Any "com.example.camera.VideoController  Boolean "com.example.camera.VideoController  Camera "com.example.camera.VideoController  CameraSelector "com.example.camera.VideoController  
CameraType "com.example.camera.VideoController  Context "com.example.camera.VideoController  
ContextCompat "com.example.camera.VideoController  Date "com.example.camera.VideoController  	Exception "com.example.camera.VideoController  FallbackStrategy "com.example.camera.VideoController  File "com.example.camera.VideoController  FileOutputOptions "com.example.camera.VideoController  Float "com.example.camera.VideoController  GlyphController "com.example.camera.VideoController  Locale "com.example.camera.VideoController  Log "com.example.camera.VideoController  Map "com.example.camera.VideoController  Quality "com.example.camera.VideoController  QualitySelector "com.example.camera.VideoController  Recorder "com.example.camera.VideoController  	Recording "com.example.camera.VideoController  SimpleDateFormat "com.example.camera.VideoController  String "com.example.camera.VideoController  Uri "com.example.camera.VideoController  VideoCapture "com.example.camera.VideoController  VideoQuality "com.example.camera.VideoController  VideoRecordEvent "com.example.camera.VideoController  android "com.example.camera.VideoController  androidx "com.example.camera.VideoController  apply "com.example.camera.VideoController  cleanup "com.example.camera.VideoController  context "com.example.camera.VideoController  createVideoFile "com.example.camera.VideoController  currentCameraType "com.example.camera.VideoController  currentVideoQuality "com.example.camera.VideoController  delay "com.example.camera.VideoController  filter "com.example.camera.VideoController  glyphController "com.example.camera.VideoController  initializeVideoCapture "com.example.camera.VideoController  isCurrentlyRecording "com.example.camera.VideoController  isRecording "com.example.camera.VideoController  isTransitioning "com.example.camera.VideoController  mapOf "com.example.camera.VideoController  performCameraTransition "com.example.camera.VideoController  	recording "com.example.camera.VideoController  repeat "com.example.camera.VideoController  smoothVideoZoom "com.example.camera.VideoController  startVideoRecording "com.example.camera.VideoController  stopVideoRecording "com.example.camera.VideoController  to "com.example.camera.VideoController  videoCapture "com.example.camera.VideoController  MAIN -com.example.camera.VideoController.CameraType  	TELEPHOTO -com.example.camera.VideoController.CameraType  	ULTRAWIDE -com.example.camera.VideoController.CameraType  name -com.example.camera.VideoController.CameraType  	FHD_30FPS /com.example.camera.VideoController.VideoQuality  	FHD_60FPS /com.example.camera.VideoController.VideoQuality  	UHD_30FPS /com.example.camera.VideoController.VideoQuality  name /com.example.camera.VideoController.VideoQuality  Finalize 3com.example.camera.VideoController.VideoRecordEvent  Start 3com.example.camera.VideoController.VideoRecordEvent  Finalize #com.example.camera.VideoRecordEvent  Start #com.example.camera.VideoRecordEvent  util com.example.camera.android  Size com.example.camera.android.util  camera com.example.camera.androidx  video "com.example.camera.androidx.camera  Recorder (com.example.camera.androidx.camera.video  VideoCapture (com.example.camera.androidx.camera.video  Boolean com.example.camera.ui.theme  Build com.example.camera.ui.theme  CameraTheme com.example.camera.ui.theme  
Composable com.example.camera.ui.theme  DarkColorScheme com.example.camera.ui.theme  
FontFamily com.example.camera.ui.theme  
FontWeight com.example.camera.ui.theme  LightColorScheme com.example.camera.ui.theme  Pink40 com.example.camera.ui.theme  Pink80 com.example.camera.ui.theme  Purple40 com.example.camera.ui.theme  Purple80 com.example.camera.ui.theme  PurpleGrey40 com.example.camera.ui.theme  PurpleGrey80 com.example.camera.ui.theme  
Typography com.example.camera.ui.theme  Unit com.example.camera.ui.theme  ExperimentalPermissionsApi "com.google.accompanist.permissions  MultiplePermissionsState "com.google.accompanist.permissions   rememberMultiplePermissionsState "com.google.accompanist.permissions  allPermissionsGranted ;com.google.accompanist.permissions.MultiplePermissionsState  launchMultiplePermissionRequest ;com.google.accompanist.permissions.MultiplePermissionsState  ListenableFuture !com.google.common.util.concurrent  get 2com.google.common.util.concurrent.ListenableFuture  File java.io  FileOutputStream java.io  absolutePath java.io.File  exists java.io.File  mkdirs java.io.File  use java.io.FileOutputStream  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  
freeMemory java.lang.Runtime  
getRuntime java.lang.Runtime  	maxMemory java.lang.Runtime  totalMemory java.lang.Runtime  gc java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Any 	java.util  Bitmap 	java.util  
BitmapFactory 	java.util  Boolean 	java.util  Camera 	java.util  CameraController 	java.util  
CameraMode 	java.util  CameraSelector 	java.util  
CameraType 	java.util  Context 	java.util  
ContextCompat 	java.util  Date 	java.util  Dispatchers 	java.util  Environment 	java.util  	Exception 	java.util  ExecutorService 	java.util  	Executors 	java.util  FallbackStrategy 	java.util  File 	java.util  FileOutputOptions 	java.util  FileOutputStream 	java.util  FilterProcessor 	java.util  
FilterType 	java.util  	FlashMode 	java.util  Float 	java.util  GlyphController 	java.util  
ImageAnalysis 	java.util  ImageCapture 	java.util  ImageCaptureException 	java.util  LifecycleOwner 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  Map 	java.util  NothingPhoneOptimizations 	java.util  PortraitProcessor 	java.util  Preview 	java.util  ProcessCameraProvider 	java.util  Quality 	java.util  QualitySelector 	java.util  Recorder 	java.util  	Recording 	java.util  SimpleDateFormat 	java.util  Size 	java.util  String 	java.util  Uri 	java.util  VideoCapture 	java.util  VideoController 	java.util  VideoQuality 	java.util  VideoRecordEvent 	java.util  android 	java.util  androidx 	java.util  apply 	java.util  applyFilterOptimized 	java.util  applyFilterToSavedImage 	java.util  applyNightModeFilter 	java.util  applyPortraitProcessing 	java.util  arrayOf 	java.util  cameraExecutor 	java.util  cameraProvider 	java.util  captureNightModePhoto 	java.util  capturePortraitPhoto 	java.util  coerceIn 	java.util  context 	java.util  createImageFile 	java.util  
currentFilter 	java.util  currentMode 	java.util  delay 	java.util  filter 	java.util  	flashMode 	java.util  glyphController 	java.util  isNightModeEnabled 	java.util  let 	java.util  listOf 	java.util  mapOf 	java.util  repeat 	java.util  setupCamera 	java.util  to 	java.util  use 	java.util  withContext 	java.util  OnImageSavedCallback java.util.ImageCapture  OutputFileResults java.util.ImageCapture  
getDefault java.util.Locale  SurfaceProvider java.util.Preview  
CameraType java.util.VideoController  Finalize java.util.VideoRecordEvent  Start java.util.VideoRecordEvent  Executor java.util.concurrent  ExecutorService java.util.concurrent  	Executors java.util.concurrent  execute java.util.concurrent.Executor  execute $java.util.concurrent.ExecutorService  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  
AtomicBoolean java.util.concurrent.atomic  
compareAndSet )java.util.concurrent.atomic.AtomicBoolean  get )java.util.concurrent.atomic.AtomicBoolean  set )java.util.concurrent.atomic.AtomicBoolean  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function4 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  
ShortArray kotlin  String kotlin  Suppress kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  also kotlin  apply kotlin  arrayOf kotlin  floatArrayOf kotlin  
intArrayOf kotlin  let kotlin  repeat kotlin  run kotlin  to kotlin  use kotlin  equals 
kotlin.Any  forEach kotlin.Array  not kotlin.Boolean  sp 
kotlin.Double  toFloat 
kotlin.Double  toInt 
kotlin.Double  toLong 
kotlin.Double  coerceIn kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  rangeTo kotlin.Float  times kotlin.Float  toDouble kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  and 
kotlin.Int  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  dec 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  shl 
kotlin.Int  shr 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  
unaryMinus 
kotlin.Int  contains kotlin.IntArray  copyOf kotlin.IntArray  get kotlin.IntArray  set kotlin.IntArray  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  times kotlin.Long  	Companion 
kotlin.String  equals 
kotlin.String  format 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  to 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  IntIterator kotlin.collections  List kotlin.collections  Map kotlin.collections  contains kotlin.collections  copyOf kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  	lastIndex kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  
plusAssign kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  contains kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  indexOf kotlin.collections.List  	lastIndex kotlin.collections.List  size kotlin.collections.List  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  resume kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  
startsWith 	kotlin.io  use 	kotlin.io  kotlin 
kotlin.jvm  abs kotlin.math  min kotlin.math  sqrt kotlin.math  	CharRange 
kotlin.ranges  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  rangeTo 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  Sequence kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  measureTimeMillis 
kotlin.system  contains kotlin.text  equals kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  	lastIndex kotlin.text  repeat kotlin.text  split kotlin.text  
startsWith kotlin.text  Any kotlinx.coroutines  
AtomicBoolean kotlinx.coroutines  Boolean kotlinx.coroutines  Build kotlinx.coroutines  CameraController kotlinx.coroutines  
CameraMode kotlinx.coroutines  CancellableContinuation kotlinx.coroutines  CompletableJob kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  	Exception kotlinx.coroutines  FilterProcessor kotlinx.coroutines  Job kotlinx.coroutines  Log kotlinx.coroutines  Long kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  Map kotlinx.coroutines  PerformanceOptimizer kotlinx.coroutines  Runtime kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  System kotlinx.coroutines  cancel kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  listOf kotlinx.coroutines  mapOf kotlinx.coroutines  measureTimeMillis kotlinx.coroutines  monitorPerformance kotlinx.coroutines  suspendCancellableCoroutine kotlinx.coroutines  to kotlinx.coroutines  withContext kotlinx.coroutines  resume *kotlinx.coroutines.CancellableContinuation  plus &kotlinx.coroutines.CoroutineDispatcher  
CameraMode !kotlinx.coroutines.CoroutineScope  
ContextCompat !kotlinx.coroutines.CoroutineScope  
FilterType !kotlinx.coroutines.CoroutineScope  	FlashMode !kotlinx.coroutines.CoroutineScope  ImageCapture !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  ProcessCameraProvider !kotlinx.coroutines.CoroutineScope  Uri !kotlinx.coroutines.CoroutineScope  VibrationEffect !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  applyFilterToSavedImage !kotlinx.coroutines.CoroutineScope  arrayOf !kotlinx.coroutines.CoroutineScope  cameraProvider !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  captureNightModePhoto !kotlinx.coroutines.CoroutineScope  capturePortraitPhoto !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  createImageFile !kotlinx.coroutines.CoroutineScope  
currentFilter !kotlinx.coroutines.CoroutineScope  currentMode !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  	flashMode !kotlinx.coroutines.CoroutineScope  glyphController !kotlinx.coroutines.CoroutineScope  isNightModeEnabled !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  monitorPerformance !kotlinx.coroutines.CoroutineScope  setupCamera !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       