{"logs": [{"outputFile": "com.example.camera.app-mergeDebugResources-52:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,895,986,1078,1173,1267,1368,1461,1556,1651,1742,1833,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,75,90,91,94,93,100,92,94,94,90,90,81,109,109,99,110,108,118,181,102,82", "endOffsets": "208,300,415,499,614,737,814,890,981,1073,1168,1262,1363,1456,1551,1646,1737,1828,1910,2020,2130,2230,2341,2450,2569,2751,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,895,986,1078,1173,1267,1368,1461,1556,1651,1742,1833,1915,2025,2135,2235,2346,2455,2574,2756,9091", "endColumns": "107,91,114,83,114,122,76,75,90,91,94,93,100,92,94,94,90,90,81,109,109,99,110,108,118,181,102,82", "endOffsets": "208,300,415,499,614,737,814,890,981,1073,1168,1262,1363,1456,1551,1646,1737,1828,1910,2020,2130,2230,2341,2450,2569,2751,2854,9169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4026,4121,7915,8012,8191,8354,8436,8532,8621,8708,8772,8836,8919,9174,9529,9608,9674", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "4116,4204,8007,8106,8273,8431,8527,8616,8703,8767,8831,8914,9002,9243,9603,9669,9790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,284,388,500,580,673,784,920,1039,1182,1263,1359,1453,1551,1669,1802,1903,2039,2173,2295,2487,2606,2724,2843,2974,3069,3160,3275,3399,3497,3602,3708,3848,3991,4094,4206,4286,4362,4446,4529,4626,4703,4782,4877,4979,5070,5162,5244,5349,5443,5537,5673,5750,5858", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "167,279,383,495,575,668,779,915,1034,1177,1258,1354,1448,1546,1664,1797,1898,2034,2168,2290,2482,2601,2719,2838,2969,3064,3155,3270,3394,3492,3597,3703,3843,3986,4089,4201,4281,4357,4441,4524,4621,4698,4777,4872,4974,5065,5157,5239,5344,5438,5532,5668,5745,5853,5947"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2859,2976,3088,3192,4209,4289,4382,4493,4629,4748,4891,4972,5068,5162,5260,5378,5511,5612,5748,5882,6004,6196,6315,6433,6552,6683,6778,6869,6984,7108,7206,7311,7417,7557,7700,7803,8111,8278,9007,9248,9432,9795,9872,9951,10046,10148,10239,10331,10413,10518,10612,10706,10842,10919,11027", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "2971,3083,3187,3299,4284,4377,4488,4624,4743,4886,4967,5063,5157,5255,5373,5506,5607,5743,5877,5999,6191,6310,6428,6547,6678,6773,6864,6979,7103,7201,7306,7412,7552,7695,7798,7910,8186,8349,9086,9326,9524,9867,9946,10041,10143,10234,10326,10408,10513,10607,10701,10837,10914,11022,11116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3304,3401,3503,3605,3706,3809,3916,9331", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3396,3498,3600,3701,3804,3911,4021,9427"}}]}]}