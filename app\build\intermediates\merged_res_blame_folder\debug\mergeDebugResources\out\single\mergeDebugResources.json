[{"merged": "com.example.camera.app-debug-54:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.camera.app-main-56:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.camera.app-debug-54:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.camera.app-debug-54:/xml_data_extraction_rules.xml.flat", "source": "com.example.camera.app-main-56:/xml/data_extraction_rules.xml"}, {"merged": "com.example.camera.app-debug-54:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.camera.app-debug-54:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.camera.app-debug-54:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.camera.app-debug-54:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.camera.app-debug-54:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.camera.app-debug-54:/anim_slide_out_left.xml.flat", "source": "com.example.camera.app-main-56:/anim/slide_out_left.xml"}, {"merged": "com.example.camera.app-debug-54:/anim_zoom_out.xml.flat", "source": "com.example.camera.app-main-56:/anim/zoom_out.xml"}, {"merged": "com.example.camera.app-debug-54:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.camera.app-debug-54:/anim_slide_in_right.xml.flat", "source": "com.example.camera.app-main-56:/anim/slide_in_right.xml"}, {"merged": "com.example.camera.app-debug-54:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.camera.app-main-56:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.camera.app-debug-54:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.camera.app-debug-54:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.camera.app-debug-54:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.camera.app-main-56:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.camera.app-debug-54:/anim_zoom_in.xml.flat", "source": "com.example.camera.app-main-56:/anim/zoom_in.xml"}, {"merged": "com.example.camera.app-debug-54:/xml_backup_rules.xml.flat", "source": "com.example.camera.app-main-56:/xml/backup_rules.xml"}, {"merged": "com.example.camera.app-debug-54:/anim_shutter_pulse.xml.flat", "source": "com.example.camera.app-main-56:/anim/shutter_pulse.xml"}, {"merged": "com.example.camera.app-debug-54:/raw_lut_vivid.png.flat", "source": "com.example.camera.app-main-56:/raw/lut_vivid.png"}, {"merged": "com.example.camera.app-debug-54:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.camera.app-main-56:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.camera.app-debug-54:/drawable_ic_launcher_background.xml.flat", "source": "com.example.camera.app-main-56:/drawable/ic_launcher_background.xml"}]