{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,894,985,1077,1172,1266,1367,1460,1555,1650,1741,1832,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,79,90,91,94,93,100,92,94,94,90,90,97,105,105,97,106,106,104,169,99,80", "endOffsets": "209,311,419,505,610,728,809,889,980,1072,1167,1261,1362,1455,1550,1645,1736,1827,1925,2031,2137,2235,2342,2449,2554,2724,2824,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,894,985,1077,1172,1266,1367,1460,1555,1650,1741,1832,1930,2036,2142,2240,2347,2454,2559,2729,9009", "endColumns": "108,101,107,85,104,117,80,79,90,91,94,93,100,92,94,94,90,90,97,105,105,97,106,106,104,169,99,80", "endOffsets": "209,311,419,505,610,728,809,889,980,1072,1167,1261,1362,1455,1550,1645,1736,1827,1925,2031,2137,2235,2342,2449,2554,2724,2824,9085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3283,3383,3485,3586,3687,3792,3897,9245", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3378,3480,3581,3682,3787,3892,4005,9341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,980,1051,1136,1224,1296,1376,1446", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,975,1046,1131,1219,1291,1371,1441,1564"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4010,4103,7836,7938,8110,8272,8354,8443,8531,8613,8683,8754,8839,9090,9441,9521,9591", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "4098,4182,7933,8034,8189,8349,8438,8526,8608,8678,8749,8834,8922,9157,9516,9586,9709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,677,786,920,1032,1175,1255,1351,1440,1532,1644,1764,1865,2002,2135,2266,2451,2574,2694,2820,2938,3027,3124,3246,3372,3466,3567,3672,3812,3959,4063,4158,4229,4307,4389,4472,4567,4643,4725,4820,4920,5011,5107,5192,5295,5387,5485,5599,5675,5776", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "164,276,390,504,581,672,781,915,1027,1170,1250,1346,1435,1527,1639,1759,1860,1997,2130,2261,2446,2569,2689,2815,2933,3022,3119,3241,3367,3461,3562,3667,3807,3954,4058,4153,4224,4302,4384,4467,4562,4638,4720,4815,4915,5006,5102,5187,5290,5382,5480,5594,5670,5771,5873"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2829,2943,3055,3169,4187,4264,4355,4464,4598,4710,4853,4933,5029,5118,5210,5322,5442,5543,5680,5813,5944,6129,6252,6372,6498,6616,6705,6802,6924,7050,7144,7245,7350,7490,7637,7741,8039,8194,8927,9162,9346,9714,9790,9872,9967,10067,10158,10254,10339,10442,10534,10632,10746,10822,10923", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "2938,3050,3164,3278,4259,4350,4459,4593,4705,4848,4928,5024,5113,5205,5317,5437,5538,5675,5808,5939,6124,6247,6367,6493,6611,6700,6797,6919,7045,7139,7240,7345,7485,7632,7736,7831,8105,8267,9004,9240,9436,9785,9867,9962,10062,10153,10249,10334,10437,10529,10627,10741,10817,10918,11020"}}]}]}