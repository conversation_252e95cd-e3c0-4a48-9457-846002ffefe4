{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3306,3406,3508,3611,3718,3822,3926,9292", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3401,3503,3606,3713,3817,3921,4032,9388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4037,4130,7863,7973,8145,8316,8398,8496,8585,8670,8736,8803,8888,9138,9501,9580,9648", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "4125,4209,7968,8068,8225,8393,8491,8580,8665,8731,8798,8883,8970,9206,9575,9643,9761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,287,420,541,622,716,828,960,1097,1249,1329,1423,1511,1604,1716,1831,1930,2062,2192,2325,2496,2621,2734,2851,2969,3058,3152,3270,3403,3502,3623,3724,3853,3988,4093,4190,4262,4348,4430,4511,4619,4704,4784,4880,4977,5069,5162,5245,5350,5445,5540,5684,5770,5885", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "169,282,415,536,617,711,823,955,1092,1244,1324,1418,1506,1599,1711,1826,1925,2057,2187,2320,2491,2616,2729,2846,2964,3053,3147,3265,3398,3497,3618,3719,3848,3983,4088,4185,4257,4343,4425,4506,4614,4699,4779,4875,4972,5064,5157,5240,5345,5440,5535,5679,5765,5880,5984"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2820,2939,3052,3185,4214,4295,4389,4501,4633,4770,4922,5002,5096,5184,5277,5389,5504,5603,5735,5865,5998,6169,6294,6407,6524,6642,6731,6825,6943,7076,7175,7296,7397,7526,7661,7766,8073,8230,8975,9211,9393,9766,9851,9931,10027,10124,10216,10309,10392,10497,10592,10687,10831,10917,11032", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "2934,3047,3180,3301,4290,4384,4496,4628,4765,4917,4997,5091,5179,5272,5384,5499,5598,5730,5860,5993,6164,6289,6402,6519,6637,6726,6820,6938,7071,7170,7291,7392,7521,7656,7761,7858,8140,8311,9052,9287,9496,9846,9926,10022,10119,10211,10304,10387,10492,10587,10682,10826,10912,11027,11131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,902,993,1085,1180,1274,1375,1468,1563,1658,1749,1840,1920,2026,2131,2229,2336,2442,2557,2718,2820", "endColumns": "110,108,111,84,104,116,78,78,90,91,94,93,100,92,94,94,90,90,79,105,104,97,106,105,114,160,101,80", "endOffsets": "211,320,432,517,622,739,818,897,988,1080,1175,1269,1370,1463,1558,1653,1744,1835,1915,2021,2126,2224,2331,2437,2552,2713,2815,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,902,993,1085,1180,1274,1375,1468,1563,1658,1749,1840,1920,2026,2131,2229,2336,2442,2557,2718,9057", "endColumns": "110,108,111,84,104,116,78,78,90,91,94,93,100,92,94,94,90,90,79,105,104,97,106,105,114,160,101,80", "endOffsets": "211,320,432,517,622,739,818,897,988,1080,1175,1269,1370,1463,1558,1653,1744,1835,1915,2021,2126,2224,2331,2437,2552,2713,2815,9133"}}]}]}