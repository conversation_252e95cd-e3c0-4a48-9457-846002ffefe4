package com.example.camera

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
// RenderScript imports removed for compatibility

enum class FilterType {
    NONE,
    VIVID,
    <PERSON><PERSON><PERSON><PERSON>,
    BR<PERSON><PERSON><PERSON>NT,
    MONO,
    <PERSON><PERSON><PERSON><PERSON>ON<PERSON>,
    NOIR
}

class FilterProcessor(private val context: Context) {
    // RenderScript removed for compatibility - using native Android graphics
    
    fun applyFilter(bitmap: Bitmap, filterType: FilterType): Bitmap {
        return when (filterType) {
            FilterType.NONE -> bitmap
            FilterType.VIVID -> applyVividFilter(bitmap)
            FilterType.DRAMATIC -> applyDramaticFilter(bitmap)
            FilterType.BRILLIANT -> applyBrilliantFilter(bitmap)
            FilterType.MONO -> applyMonoFilter(bitmap)
            FilterType.SILVERTONE -> applySilvertoneFilter(bitmap)
            FilterType.NOIR -> applyNoirFilter(bitmap)
        }
    }
    
    private fun applyVividFilter(bitmap: Bitmap): Bitmap {
        // iPhone-like Vivid filter: Enhanced saturation, contrast, and warmth
        val colorMatrix = ColorMatrix().apply {
            // Increase saturation
            setSaturation(1.3f)
            
            // Add warmth and enhance colors
            val warmMatrix = ColorMatrix(floatArrayOf(
                1.1f, 0.0f, 0.0f, 0.0f, 10f,   // Red channel - slightly enhanced with warmth
                0.0f, 1.05f, 0.0f, 0.0f, 5f,   // Green channel - slight enhancement
                0.0f, 0.0f, 0.95f, 0.0f, -5f,  // Blue channel - slightly reduced for warmth
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f   // Alpha channel
            ))
            postConcat(warmMatrix)
            
            // Enhance contrast
            val contrastMatrix = ColorMatrix().apply {
                set(floatArrayOf(
                    1.15f, 0.0f, 0.0f, 0.0f, -15f,
                    0.0f, 1.15f, 0.0f, 0.0f, -15f,
                    0.0f, 0.0f, 1.15f, 0.0f, -15f,
                    0.0f, 0.0f, 0.0f, 1.0f, 0.0f
                ))
            }
            postConcat(contrastMatrix)
        }
        
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    private fun applyDramaticFilter(bitmap: Bitmap): Bitmap {
        // High contrast, enhanced shadows and highlights
        val colorMatrix = ColorMatrix().apply {
            setSaturation(1.2f)
            
            val dramaticMatrix = ColorMatrix(floatArrayOf(
                1.3f, 0.0f, 0.0f, 0.0f, -20f,
                0.0f, 1.3f, 0.0f, 0.0f, -20f,
                0.0f, 0.0f, 1.3f, 0.0f, -20f,
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f
            ))
            postConcat(dramaticMatrix)
        }
        
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    private fun applyBrilliantFilter(bitmap: Bitmap): Bitmap {
        // Bright, enhanced highlights
        val colorMatrix = ColorMatrix().apply {
            setSaturation(1.1f)
            
            val brilliantMatrix = ColorMatrix(floatArrayOf(
                1.2f, 0.0f, 0.0f, 0.0f, 15f,
                0.0f, 1.2f, 0.0f, 0.0f, 15f,
                0.0f, 0.0f, 1.2f, 0.0f, 15f,
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f
            ))
            postConcat(brilliantMatrix)
        }
        
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    private fun applyMonoFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix().apply {
            setSaturation(0f) // Remove all color
        }
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    private fun applySilvertoneFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix().apply {
            setSaturation(0f)
            
            // Add slight blue tint for silver tone
            val silverMatrix = ColorMatrix(floatArrayOf(
                0.95f, 0.0f, 0.0f, 0.0f, 0f,
                0.0f, 0.95f, 0.0f, 0.0f, 0f,
                0.0f, 0.0f, 1.05f, 0.0f, 10f,
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f
            ))
            postConcat(silverMatrix)
        }
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    private fun applyNoirFilter(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix().apply {
            setSaturation(0f)
            
            // High contrast black and white
            val noirMatrix = ColorMatrix(floatArrayOf(
                1.5f, 0.0f, 0.0f, 0.0f, -30f,
                0.0f, 1.5f, 0.0f, 0.0f, -30f,
                0.0f, 0.0f, 1.5f, 0.0f, -30f,
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f
            ))
            postConcat(noirMatrix)
        }
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    private fun applyColorMatrix(bitmap: Bitmap, colorMatrix: ColorMatrix): Bitmap {
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
        val canvas = Canvas(result)
        val paint = Paint().apply {
            colorFilter = ColorMatrixColorFilter(colorMatrix)
        }
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return result
    }
    
    // Enhanced night mode processing for Sony IMX 766
    fun applyNightModeEnhancement(bitmap: Bitmap): Bitmap {
        val colorMatrix = ColorMatrix().apply {
            // Enhance shadows and reduce noise
            val nightMatrix = ColorMatrix(floatArrayOf(
                1.2f, 0.0f, 0.0f, 0.0f, 20f,   // Boost red channel slightly
                0.0f, 1.2f, 0.0f, 0.0f, 20f,   // Boost green channel slightly
                0.0f, 0.0f, 1.1f, 0.0f, 15f,   // Boost blue channel less
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f   // Alpha unchanged
            ))
            postConcat(nightMatrix)

            // Increase overall brightness for night shots
            val brightnessMatrix = ColorMatrix().apply {
                set(floatArrayOf(
                    1.3f, 0.0f, 0.0f, 0.0f, 30f,
                    0.0f, 1.3f, 0.0f, 0.0f, 30f,
                    0.0f, 0.0f, 1.3f, 0.0f, 30f,
                    0.0f, 0.0f, 0.0f, 1.0f, 0.0f
                ))
            }
            postConcat(brightnessMatrix)
        }

        return applyColorMatrix(bitmap, colorMatrix)
    }

    fun cleanup() {
        // No cleanup needed - RenderScript removed
    }
}

// Extension function for optimized filter processing on Snapdragon 778G+
fun FilterProcessor.applyFilterOptimized(bitmap: Bitmap, filterType: FilterType): Bitmap {
    // Use GPU acceleration when available
    return try {
        applyFilter(bitmap, filterType)
    } catch (e: Exception) {
        // Fallback to CPU processing
        applyFilter(bitmap, filterType)
    }
}
