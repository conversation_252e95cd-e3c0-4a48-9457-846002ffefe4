# 📱 Building Nothing Phone One Camera App with VS Code

## 🔧 **VS Code Setup for Android Development**

### **1. Required Extensions**

Install these VS Code extensions:

```
1. Kotlin Language (fwcd.kotlin)
2. Android iOS Emulator (DiemasMichiels.emulate)
3. Gradle for Java (vscjava.vscode-gradle)
4. Extension Pack for Java (vscjava.vscode-java-pack)
5. Android Studio Tools (adelphes.android-dev-ext)
```

**Install via VS Code:**
- Press `Ctrl+Shift+X`
- Search for each extension name
- Click "Install"

### **2. Java Development Kit (JDK) Setup**

**Option A: Use Android Studio's JDK**
```bash
# Set environment variables in Windows
set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
set PATH=%JAVA_HOME%\bin;%PATH%
```

**Option B: Install JDK 11+ separately**
```bash
# Download from: https://adoptium.net/
# Install and set JAVA_HOME to installation path
```

### **3. Android SDK Setup**

**If you have Android Studio installed:**
```bash
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%
```

**If you don't have Android Studio:**
1. Download Android SDK Command Line Tools
2. Extract to `C:\Android\sdk`
3. Set `ANDROID_HOME=C:\Android\sdk`

### **4. Gradle Setup**

**Check if Gradle is working:**
```bash
# Open terminal in VS Code (Ctrl+`)
gradlew --version
```

If not working, download Gradle from https://gradle.org/

## 🚀 **Building the App in VS Code**

### **1. Open Project**
```bash
# Open VS Code
code .

# Or open folder
File → Open Folder → Select your Camera project
```

### **2. Configure VS Code Settings**

Create `.vscode/settings.json`:
```json
{
    "java.home": "C:\\Program Files\\Android\\Android Studio\\jbr",
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-11",
            "path": "C:\\Program Files\\Android\\Android Studio\\jbr"
        }
    ],
    "gradle.nestedProjects": true,
    "files.exclude": {
        "**/build": true,
        "**/.gradle": true
    }
}
```

### **3. Build Commands**

**Open Terminal in VS Code (`Ctrl+``):**

```bash
# Clean build
gradlew clean

# Build debug APK
gradlew assembleDebug

# Build release APK
gradlew assembleRelease

# Install on connected device
gradlew installDebug
```

### **4. VS Code Tasks (Optional)**

Create `.vscode/tasks.json` for quick build tasks:
```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Clean Build",
            "type": "shell",
            "command": "./gradlew",
            "args": ["clean"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "Build Debug APK",
            "type": "shell",
            "command": "./gradlew",
            "args": ["assembleDebug"],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "Build Release APK",
            "type": "shell",
            "command": "./gradlew",
            "args": ["assembleRelease"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "Install on Device",
            "type": "shell",
            "command": "./gradlew",
            "args": ["installDebug"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        }
    ]
}
```

**Use tasks with `Ctrl+Shift+P` → "Tasks: Run Task"**

## 📱 **Building & Installing**

### **1. Build Debug APK**
```bash
# In VS Code terminal
gradlew assembleDebug
```

**Output location:**
```
app\build\outputs\apk\debug\app-debug.apk
```

### **2. Build Release APK**
```bash
# For production release
gradlew assembleRelease
```

### **3. Install on Nothing Phone One**
```bash
# Enable Developer Options & USB Debugging on your phone
# Connect via USB

# Install debug version
adb install app\build\outputs\apk\debug\app-debug.apk

# Or use Gradle
gradlew installDebug
```

## 🔧 **VS Code Advantages**

### **Why VS Code is Great for This Project:**
- ✅ **Lightweight**: Faster than Android Studio
- ✅ **Kotlin Support**: Full syntax highlighting and IntelliSense
- ✅ **Terminal Integration**: Direct Gradle commands
- ✅ **Git Integration**: Built-in version control
- ✅ **Customizable**: Extensive extension ecosystem
- ✅ **Cross-platform**: Works on Windows, Mac, Linux

### **Performance Benefits:**
- ✅ **Faster startup** than Android Studio
- ✅ **Less memory usage**
- ✅ **Quick file navigation**
- ✅ **Efficient code editing**

## 🎯 **Quick Build Script for VS Code**

Create `build.bat` in your project root:
```batch
@echo off
echo ========================================
echo  Nothing Phone Camera - VS Code Build
echo ========================================

echo.
echo Building debug APK...
call gradlew assembleDebug

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo.
    echo ✅ BUILD SUCCESSFUL!
    echo APK: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Install on Nothing Phone One:
    echo adb install app\build\outputs\apk\debug\app-debug.apk
) else (
    echo.
    echo ❌ BUILD FAILED!
    echo Check the error messages above.
)

pause
```

**Run with:** `./build.bat` in VS Code terminal

## 🚀 **Release Build Process**

### **1. Generate Signed APK**

**Create keystore (one-time setup):**
```bash
keytool -genkey -v -keystore camera-release-key.keystore -alias camera -keyalg RSA -keysize 2048 -validity 10000
```

**Add to `app/build.gradle.kts`:**
```kotlin
android {
    signingConfigs {
        create("release") {
            storeFile = file("../camera-release-key.keystore")
            storePassword = "your_store_password"
            keyAlias = "camera"
            keyPassword = "your_key_password"
        }
    }
    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
}
```

**Build signed release:**
```bash
gradlew assembleRelease
```

### **2. Upload to Play Store**

1. **Create Google Play Console account**
2. **Upload APK** from `app\build\outputs\apk\release\`
3. **Fill app details** and screenshots
4. **Publish** your Nothing Phone One camera app!

## ✅ **VS Code is Perfect for This Project**

**Your Nothing Phone One camera app will build perfectly in VS Code with:**
- ✅ **All iPhone 15 Pro features** working
- ✅ **Glyph integration** functional
- ✅ **Smooth 60fps performance**
- ✅ **Professional camera experience**
- ✅ **Optimized for Snapdragon 778G+**

**VS Code gives you full control over the build process while being much lighter than Android Studio!** 🚀📱
