{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,904,995,1087,1183,1277,1378,1471,1566,1662,1753,1844,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,81,90,91,95,93,100,92,94,95,90,90,85,105,105,100,106,111,103,155,97,83", "endOffsets": "208,312,420,506,614,733,817,899,990,1082,1178,1272,1373,1466,1561,1657,1748,1839,1925,2031,2137,2238,2345,2457,2561,2717,2815,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,904,995,1087,1183,1277,1378,1471,1566,1662,1753,1844,1930,2036,2142,2243,2350,2462,2566,2722,9034", "endColumns": "107,103,107,85,107,118,83,81,90,91,95,93,100,92,94,95,90,90,85,105,105,100,106,111,103,155,97,83", "endOffsets": "208,312,420,506,614,733,817,899,990,1082,1178,1272,1373,1466,1561,1657,1748,1839,1925,2031,2137,2238,2345,2457,2561,2717,2815,9113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3275,3373,3475,3572,3670,3775,3878,9272", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3368,3470,3567,3665,3770,3873,3989,9368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1393,1464", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1388,1459,1580"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3994,4098,7855,7951,8127,8289,8366,8456,8548,8632,8703,8773,8857,9118,9483,9564,9635", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "4093,4183,7946,8049,8207,8361,8451,8543,8627,8698,8768,8852,8941,9185,9559,9630,9751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,284,396,510,586,677,786,915,1032,1176,1257,1352,1442,1534,1645,1767,1867,2008,2148,2286,2454,2581,2698,2822,2942,3033,3127,3249,3380,3476,3574,3683,3821,3968,4080,4177,4250,4327,4415,4497,4607,4691,4770,4867,4965,5058,5151,5235,5338,5434,5531,5660,5742,5849", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "165,279,391,505,581,672,781,910,1027,1171,1252,1347,1437,1529,1640,1762,1862,2003,2143,2281,2449,2576,2693,2817,2937,3028,3122,3244,3375,3471,3569,3678,3816,3963,4075,4172,4245,4322,4410,4492,4602,4686,4765,4862,4960,5053,5146,5230,5333,5429,5526,5655,5737,5844,5943"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2820,2935,3049,3161,4188,4264,4355,4464,4593,4710,4854,4935,5030,5120,5212,5323,5445,5545,5686,5826,5964,6132,6259,6376,6500,6620,6711,6805,6927,7058,7154,7252,7361,7499,7646,7758,8054,8212,8946,9190,9373,9756,9840,9919,10016,10114,10207,10300,10384,10487,10583,10680,10809,10891,10998", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "2930,3044,3156,3270,4259,4350,4459,4588,4705,4849,4930,5025,5115,5207,5318,5440,5540,5681,5821,5959,6127,6254,6371,6495,6615,6706,6800,6922,7053,7149,7247,7356,7494,7641,7753,7850,8122,8284,9029,9267,9478,9835,9914,10011,10109,10202,10295,10379,10482,10578,10675,10804,10886,10993,11092"}}]}]}