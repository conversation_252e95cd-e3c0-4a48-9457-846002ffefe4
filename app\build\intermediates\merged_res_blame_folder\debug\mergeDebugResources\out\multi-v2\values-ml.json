{"logs": [{"outputFile": "com.example.camera.app-mergeDebugResources-52:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,901,992,1084,1185,1279,1380,1474,1569,1668,1759,1850,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,75,90,91,100,93,100,93,94,98,90,90,80,108,103,98,111,111,120,164,100,81", "endOffsets": "207,313,424,515,620,742,820,896,987,1079,1180,1274,1375,1469,1564,1663,1754,1845,1926,2035,2139,2238,2350,2462,2583,2748,2849,2931"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,901,992,1084,1185,1279,1380,1474,1569,1668,1759,1850,1931,2040,2144,2243,2355,2467,2588,2753,9206", "endColumns": "106,105,110,90,104,121,77,75,90,91,100,93,100,93,94,98,90,90,80,108,103,98,111,111,120,164,100,81", "endOffsets": "207,313,424,515,620,742,820,896,987,1079,1180,1274,1375,1469,1564,1663,1754,1845,1926,2035,2139,2238,2350,2462,2583,2748,2849,9283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1009,1076,1162,1249,1327,1403,1470", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1004,1071,1157,1244,1322,1398,1465,1584"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4070,4165,8007,8106,8282,8454,8540,8641,8728,8816,8883,8950,9036,9288,9664,9740,9807", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "4160,4247,8101,8205,8367,8535,8636,8723,8811,8878,8945,9031,9118,9361,9735,9802,9921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,293,415,534,614,704,812,960,1074,1227,1308,1409,1505,1603,1722,1851,1957,2089,2227,2354,2554,2684,2804,2931,3062,3159,3253,3367,3494,3589,3687,3796,3933,4075,4185,4289,4361,4443,4526,4619,4723,4799,4883,4977,5086,5177,5284,5370,5481,5579,5687,5815,5891,5994", "endColumns": "115,121,121,118,79,89,107,147,113,152,80,100,95,97,118,128,105,131,137,126,199,129,119,126,130,96,93,113,126,94,97,108,136,141,109,103,71,81,82,92,103,75,83,93,108,90,106,85,110,97,107,127,75,102,92", "endOffsets": "166,288,410,529,609,699,807,955,1069,1222,1303,1404,1500,1598,1717,1846,1952,2084,2222,2349,2549,2679,2799,2926,3057,3154,3248,3362,3489,3584,3682,3791,3928,4070,4180,4284,4356,4438,4521,4614,4718,4794,4878,4972,5081,5172,5279,5365,5476,5574,5682,5810,5886,5989,6082"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2970,3092,3214,4252,4332,4422,4530,4678,4792,4945,5026,5127,5223,5321,5440,5569,5675,5807,5945,6072,6272,6402,6522,6649,6780,6877,6971,7085,7212,7307,7405,7514,7651,7793,7903,8210,8372,9123,9366,9560,9926,10002,10086,10180,10289,10380,10487,10573,10684,10782,10890,11018,11094,11197", "endColumns": "115,121,121,118,79,89,107,147,113,152,80,100,95,97,118,128,105,131,137,126,199,129,119,126,130,96,93,113,126,94,97,108,136,141,109,103,71,81,82,92,103,75,83,93,108,90,106,85,110,97,107,127,75,102,92", "endOffsets": "2965,3087,3209,3328,4327,4417,4525,4673,4787,4940,5021,5122,5218,5316,5435,5564,5670,5802,5940,6067,6267,6397,6517,6644,6775,6872,6966,7080,7207,7302,7400,7509,7646,7788,7898,8002,8277,8449,9201,9454,9659,9997,10081,10175,10284,10375,10482,10568,10679,10777,10885,11013,11089,11192,11285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3333,3435,3538,3640,3744,3847,3948,9459", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3430,3533,3635,3739,3842,3943,4065,9555"}}]}]}