-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:2:1-69:12
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f632b74f54372928d8237ddad3b1b82\transformed\camera-video-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aea1891d23fd327287f58962aca339d\transformed\camera-view-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9017705fce12f909ede8ba9f78321443\transformed\camera-lifecycle-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9e5113d013b711d1b33991090c23cc9\transformed\camera-core-1.3.0\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7982a989e315c5ea24cc186b05f9e71f\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e540a684c298e285281dc74ad14fe0b\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0651e95cd0e8d6c115791cc868f86d2\transformed\material3-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e612e097068a3967fbbad72793907042\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30cced2c32333f1dda59fb97c4ecb3e2\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61080898e56456f89206d7a6a47c994\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0df872eb281e5a60894de4451731060\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4538a70bf2b17619cd3f3585309418de\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\703cc7c087567ab08048e49563def836\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b75989bff4816fbd2b308aa1592c4de\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2158350ca875f6cb81f5573b2f38f780\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c94c3b26008b90b1e7cfdb24939743b\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a3b4d7d934c27b4f27058875a3b0658\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\854fe51b910ce7a78f6d92126ede961f\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac01e2930faa5f42eee97c6bce7f89ad\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\610616935c13ffdc2b01ebb038cc705c\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db5fe0686f5d0af39216432c7831bb50\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\271c235c502cf00a851534bd0a06470b\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9de52b67cfb03282245bdbb4f8195f0\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\374961183eaa7640a4b55ae5eded5e4a\transformed\activity-compose-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d84e605c8d82e4c9cd0ca8724ae59fa7\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12546c0b1520cc851649e7f5c69150e2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f52d47342be363b65f536ca25226a98b\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\701f528b73d4368f950875470101a816\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d08a6bf331565b9a6aae0b5f313b0964\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f9b2f3ac4e57c2a361a436e16b1cb4e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5ac56bda58df08e077885f4b12082e7\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\870a53137907ee02f59df93fb67b66e6\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45321f74a8e4ea570c23332585254c7b\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8fd4810cab3bf3d4d865929f1076f42\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984736a25314e03e89d0551751b4916c\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d923302443db812770a531d84fe1bab8\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3350dd5aeb7c8ab0d36dc3637f12310f\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fce76da01519a7e775994f3de797a74\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4723f63d599634bdb83709f096c017fd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65049f970678ed445dbf29acaa5226fc\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d7f3461144cf50799f37105b86ea65\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a8f5bbf0be5cef9228bdc8aaca706b9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab857242c4b95d10e3ce9a2ce1208eb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c56129fe800ddf3800701b25673db6af\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fb45d41e6a7dc95d863a72eae7b4c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41dfeded8a9f4faa63bd75c323972a58\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b81068fd88751ca38f4894fa961fe69\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aee75268b4fbbb354e5aaff49621aa88\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5fa1c0bec0e5712959f57bf8e2e8291\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91825d9b5c7305e30c21938a9839cc92\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\557822b0d272c7188dc407336b6e2f3b\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a38ab311f18dd3ee25dbd9e1f09f96\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b164298045610576b2a25c9cb4c23e32\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b242992e83cfa1bb20c68975f3be6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed8cbef73ef5c5535d65f8a6243078ba\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.camera.any
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:5-88
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:62-85
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:19-61
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:5-84
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:58-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:5-94
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:68-91
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:19-67
uses-feature#android.hardware.camera.flash
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:5-91
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:64-88
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:19-63
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:12:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:12:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:13:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:13:22-68
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:14:5-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:14:22-63
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:17:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:17:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:18:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:18:22-72
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:19:5-20:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:20:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:19:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:21:5-22:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:22:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:21:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:25:5-26:40
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:26:9-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:25:22-79
uses-permission#com.nothing.ketchum.permission.LIGHTS
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:29:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:29:22-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:32:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:32:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:33:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:33:22-76
queries
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:36:5-43:15
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:22:5-26:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:37:9-39:18
action#android.media.action.IMAGE_CAPTURE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:38:13-73
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:38:21-70
intent#action:name:android.intent.action.GET_CONTENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:40:9-42:18
action#android.intent.action.GET_CONTENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:41:21-69
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:45:5-67:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:45:5-67:19
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9e5113d013b711d1b33991090c23cc9\transformed\camera-core-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9e5113d013b711d1b33991090c23cc9\transformed\camera-core-1.3.0\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:52:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:50:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:48:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:51:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:54:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:49:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:46:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:53:9-44
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:47:9-65
activity#com.example.camera.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:56:9-66:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:61:13-49
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:59:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:58:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:60:13-48
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:57:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:62:13-65:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:63:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:63:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:64:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:64:27-74
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f632b74f54372928d8237ddad3b1b82\transformed\camera-video-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f632b74f54372928d8237ddad3b1b82\transformed\camera-video-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aea1891d23fd327287f58962aca339d\transformed\camera-view-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aea1891d23fd327287f58962aca339d\transformed\camera-view-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9017705fce12f909ede8ba9f78321443\transformed\camera-lifecycle-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9017705fce12f909ede8ba9f78321443\transformed\camera-lifecycle-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9e5113d013b711d1b33991090c23cc9\transformed\camera-core-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9e5113d013b711d1b33991090c23cc9\transformed\camera-core-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7982a989e315c5ea24cc186b05f9e71f\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7982a989e315c5ea24cc186b05f9e71f\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e540a684c298e285281dc74ad14fe0b\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e540a684c298e285281dc74ad14fe0b\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0651e95cd0e8d6c115791cc868f86d2\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0651e95cd0e8d6c115791cc868f86d2\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e612e097068a3967fbbad72793907042\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e612e097068a3967fbbad72793907042\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30cced2c32333f1dda59fb97c4ecb3e2\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30cced2c32333f1dda59fb97c4ecb3e2\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61080898e56456f89206d7a6a47c994\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61080898e56456f89206d7a6a47c994\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0df872eb281e5a60894de4451731060\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0df872eb281e5a60894de4451731060\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4538a70bf2b17619cd3f3585309418de\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4538a70bf2b17619cd3f3585309418de\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\703cc7c087567ab08048e49563def836\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\703cc7c087567ab08048e49563def836\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b75989bff4816fbd2b308aa1592c4de\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b75989bff4816fbd2b308aa1592c4de\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2158350ca875f6cb81f5573b2f38f780\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2158350ca875f6cb81f5573b2f38f780\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c94c3b26008b90b1e7cfdb24939743b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c94c3b26008b90b1e7cfdb24939743b\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a3b4d7d934c27b4f27058875a3b0658\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a3b4d7d934c27b4f27058875a3b0658\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\854fe51b910ce7a78f6d92126ede961f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\854fe51b910ce7a78f6d92126ede961f\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac01e2930faa5f42eee97c6bce7f89ad\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac01e2930faa5f42eee97c6bce7f89ad\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\610616935c13ffdc2b01ebb038cc705c\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\610616935c13ffdc2b01ebb038cc705c\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db5fe0686f5d0af39216432c7831bb50\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db5fe0686f5d0af39216432c7831bb50\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\271c235c502cf00a851534bd0a06470b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\271c235c502cf00a851534bd0a06470b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9de52b67cfb03282245bdbb4f8195f0\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9de52b67cfb03282245bdbb4f8195f0\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\374961183eaa7640a4b55ae5eded5e4a\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\374961183eaa7640a4b55ae5eded5e4a\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d84e605c8d82e4c9cd0ca8724ae59fa7\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d84e605c8d82e4c9cd0ca8724ae59fa7\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12546c0b1520cc851649e7f5c69150e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12546c0b1520cc851649e7f5c69150e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f52d47342be363b65f536ca25226a98b\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f52d47342be363b65f536ca25226a98b\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\701f528b73d4368f950875470101a816\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\701f528b73d4368f950875470101a816\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d08a6bf331565b9a6aae0b5f313b0964\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d08a6bf331565b9a6aae0b5f313b0964\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f9b2f3ac4e57c2a361a436e16b1cb4e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f9b2f3ac4e57c2a361a436e16b1cb4e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5ac56bda58df08e077885f4b12082e7\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5ac56bda58df08e077885f4b12082e7\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\870a53137907ee02f59df93fb67b66e6\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\870a53137907ee02f59df93fb67b66e6\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45321f74a8e4ea570c23332585254c7b\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45321f74a8e4ea570c23332585254c7b\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8fd4810cab3bf3d4d865929f1076f42\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8fd4810cab3bf3d4d865929f1076f42\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984736a25314e03e89d0551751b4916c\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\984736a25314e03e89d0551751b4916c\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d923302443db812770a531d84fe1bab8\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d923302443db812770a531d84fe1bab8\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3350dd5aeb7c8ab0d36dc3637f12310f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3350dd5aeb7c8ab0d36dc3637f12310f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fce76da01519a7e775994f3de797a74\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fce76da01519a7e775994f3de797a74\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4723f63d599634bdb83709f096c017fd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4723f63d599634bdb83709f096c017fd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65049f970678ed445dbf29acaa5226fc\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65049f970678ed445dbf29acaa5226fc\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d7f3461144cf50799f37105b86ea65\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d7f3461144cf50799f37105b86ea65\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a8f5bbf0be5cef9228bdc8aaca706b9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a8f5bbf0be5cef9228bdc8aaca706b9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab857242c4b95d10e3ce9a2ce1208eb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab857242c4b95d10e3ce9a2ce1208eb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c56129fe800ddf3800701b25673db6af\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c56129fe800ddf3800701b25673db6af\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fb45d41e6a7dc95d863a72eae7b4c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fb45d41e6a7dc95d863a72eae7b4c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41dfeded8a9f4faa63bd75c323972a58\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41dfeded8a9f4faa63bd75c323972a58\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b81068fd88751ca38f4894fa961fe69\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b81068fd88751ca38f4894fa961fe69\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aee75268b4fbbb354e5aaff49621aa88\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aee75268b4fbbb354e5aaff49621aa88\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5fa1c0bec0e5712959f57bf8e2e8291\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5fa1c0bec0e5712959f57bf8e2e8291\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91825d9b5c7305e30c21938a9839cc92\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91825d9b5c7305e30c21938a9839cc92\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\557822b0d272c7188dc407336b6e2f3b\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\557822b0d272c7188dc407336b6e2f3b\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a38ab311f18dd3ee25dbd9e1f09f96\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a38ab311f18dd3ee25dbd9e1f09f96\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b164298045610576b2a25c9cb4c23e32\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b164298045610576b2a25c9cb4c23e32\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b242992e83cfa1bb20c68975f3be6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b242992e83cfa1bb20c68975f3be6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed8cbef73ef5c5535d65f8a6243078ba\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed8cbef73ef5c5535d65f8a6243078ba\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e540a684c298e285281dc74ad14fe0b\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:23:9-39
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9e5113d013b711d1b33991090c23cc9\transformed\camera-core-1.3.0\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9e5113d013b711d1b33991090c23cc9\transformed\camera-core-1.3.0\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.example.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
