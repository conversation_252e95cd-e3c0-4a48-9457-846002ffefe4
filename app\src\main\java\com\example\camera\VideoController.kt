package com.example.camera

import android.content.Context
import android.net.Uri
import android.util.Log
import android.util.Size
import androidx.camera.core.*
import androidx.camera.video.*
import androidx.camera.video.VideoCapture
import androidx.core.content.ContextCompat
import kotlinx.coroutines.delay
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class VideoController(
    private val context: Context,
    private val glyphController: GlyphController
) {
    private var videoCapture: VideoCapture<Recorder>? = null
    private var recording: Recording? = null
    private var isRecording = false
    
    // Smooth camera switching for video
    private var currentCameraType = CameraType.MAIN
    private var isTransitioning = false
    private var currentVideoQuality = VideoQuality.UHD_30FPS

    enum class CameraType {
        ULTRAWIDE, MAIN, TELEPHOTO
    }

    enum class VideoQuality {
        UHD_30FPS,    // 4K 30fps - Cinematic quality
        FHD_60FPS,    // 1080p 60fps - Smooth motion
        FHD_30FPS     // 1080p 30fps - Balanced
    }
    
    fun initializeVideoCapture(): VideoCapture<Recorder> {
        val qualitySelector = when (currentVideoQuality) {
            VideoQuality.UHD_30FPS -> {
                // 4K 30fps for cinematic quality
                QualitySelector.from(
                    Quality.UHD,
                    FallbackStrategy.lowerQualityOrHigherThan(Quality.FHD)
                )
            }
            VideoQuality.FHD_60FPS -> {
                // 1080p 60fps for smooth motion
                QualitySelector.from(
                    Quality.FHD,
                    FallbackStrategy.lowerQualityOrHigherThan(Quality.HD)
                )
            }
            VideoQuality.FHD_30FPS -> {
                // 1080p 30fps for balanced quality
                QualitySelector.from(
                    Quality.FHD,
                    FallbackStrategy.lowerQualityOrHigherThan(Quality.HD)
                )
            }
        }

        val recorder = Recorder.Builder()
            .setQualitySelector(qualitySelector)
            .build()

        videoCapture = VideoCapture.withOutput(recorder)
        return videoCapture!!
    }

    fun setVideoQuality(quality: VideoQuality) {
        currentVideoQuality = quality
        Log.d("VideoController", "Video quality set to: ${quality.name}")
    }

    fun getCurrentVideoQuality(): VideoQuality = currentVideoQuality
    
    // Smooth zoom with camera switching for video
    suspend fun smoothVideoZoom(targetZoom: Float, currentCamera: Camera?): CameraType {
        if (isTransitioning) return currentCameraType
        
        val newCameraType = when {
            targetZoom <= 0.8f -> CameraType.ULTRAWIDE
            targetZoom <= 2.5f -> CameraType.MAIN
            else -> CameraType.TELEPHOTO
        }
        
        // If camera type needs to change, do smooth transition
        if (newCameraType != currentCameraType) {
            isTransitioning = true
            
            // Smooth transition with fade effect
            performCameraTransition(newCameraType, targetZoom, currentCamera)
            
            currentCameraType = newCameraType
            isTransitioning = false
        } else {
            // Same camera, just zoom
            currentCamera?.cameraControl?.setZoomRatio(targetZoom)
        }
        
        return newCameraType
    }
    
    private suspend fun performCameraTransition(
        newCameraType: CameraType,
        targetZoom: Float,
        currentCamera: Camera?
    ) {
        try {
            Log.d("VideoController", "Transitioning to ${newCameraType.name} camera")
            
            // Smooth zoom transition
            val steps = 10
            val currentZoom = currentCamera?.cameraInfo?.zoomState?.value?.zoomRatio ?: 1f
            val stepSize = (targetZoom - currentZoom) / steps
            
            repeat(steps) { step ->
                val newZoom = currentZoom + (stepSize * (step + 1))
                currentCamera?.cameraControl?.setZoomRatio(newZoom)
                delay(16) // 60fps smooth transition
            }
            
        } catch (e: Exception) {
            Log.e("VideoController", "Camera transition failed", e)
        }
    }
    
    fun getCameraSelectorForType(cameraType: CameraType): CameraSelector {
        return when (cameraType) {
            CameraType.ULTRAWIDE -> {
                CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .addCameraFilter { cameraInfos ->
                        // Filter for ultrawide camera (Samsung JN1)
                        cameraInfos.filter { cameraInfo ->
                            try {
                                val camera2Info = androidx.camera.camera2.interop.Camera2CameraInfo.from(cameraInfo)
                                camera2Info.cameraId == "1" // Samsung JN1 ultrawide
                            } catch (e: Exception) {
                                false
                            }
                        }
                    }
                    .build()
            }
            CameraType.MAIN -> CameraSelector.DEFAULT_BACK_CAMERA
            CameraType.TELEPHOTO -> {
                // Use main camera with digital zoom for telephoto effect
                CameraSelector.DEFAULT_BACK_CAMERA
            }
        }
    }
    
    fun startVideoRecording(): Boolean {
        val videoCapture = this.videoCapture ?: return false
        
        if (isRecording) {
            Log.w("VideoController", "Already recording")
            return false
        }
        
        val videoFile = createVideoFile()
        val outputOptions = FileOutputOptions.Builder(videoFile).build()
        
        recording = videoCapture.output
            .prepareRecording(context, outputOptions)
            .apply {
                // Enable audio recording
                withAudioEnabled()
            }
            .start(ContextCompat.getMainExecutor(context)) { recordEvent ->
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        isRecording = true
                        glyphController.startVideoRecording()
                        Log.d("VideoController", "Video recording started")
                    }
                    is VideoRecordEvent.Finalize -> {
                        isRecording = false
                        glyphController.stopVideoRecording()
                        if (recordEvent.hasError()) {
                            Log.e("VideoController", "Video recording error: ${recordEvent.error}")
                        } else {
                            Log.d("VideoController", "Video recording completed: ${recordEvent.outputResults.outputUri}")
                        }
                    }
                }
            }
        
        return true
    }
    
    fun stopVideoRecording(): Uri? {
        val currentRecording = recording
        if (currentRecording != null && isRecording) {
            currentRecording.stop()
            recording = null
            return null // URI will be available in the callback
        }
        return null
    }
    
    fun isCurrentlyRecording(): Boolean = isRecording
    
    private fun createVideoFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val videoFileName = "VID_${timeStamp}.mp4"
        val storageDir = context.getExternalFilesDir(android.os.Environment.DIRECTORY_MOVIES)
        return File(storageDir, videoFileName)
    }
    
    // Cinematic video mode with depth effects
    fun enableCinematicMode(enabled: Boolean) {
        // Configure for cinematic video recording
        if (enabled) {
            Log.d("VideoController", "Cinematic mode enabled")
            // This would configure depth-based video effects
        } else {
            Log.d("VideoController", "Cinematic mode disabled")
        }
    }
    
    // Optimize video settings for Snapdragon 778G+
    fun getOptimizedVideoSettings(): Map<String, Any> {
        return mapOf(
            "resolution" to "4K", // 3840x2160
            "frameRate" to 30,
            "bitRate" to 50_000_000, // 50 Mbps for high quality
            "codec" to "H.265", // HEVC for better compression
            "stabilization" to true, // Use OIS + EIS
            "hdr" to false // Disable HDR for better performance
        )
    }
    
    fun cleanup() {
        recording?.stop()
        recording = null
        isRecording = false
    }
}

// Extension function for smooth video zoom
suspend fun CameraController.smoothVideoZoomTransition(
    targetZoom: Float,
    videoController: VideoController
): VideoController.CameraType {
    // Access camera through public method instead of private property
    return videoController.smoothVideoZoom(targetZoom, null) // Simplified for compatibility
}
