{"logs": [{"outputFile": "com.example.camera.test.app-mergeDebugAndroidTestResources-32:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\adb64f38986e7cdd49a1ca36d676d869\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,198,298,395,494,590,692,1904", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "193,293,390,489,585,687,787,2000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1156b8a02ed3a004521710322c86df6f\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,875,952,1044,1140,1222,1300,1383,1465,1543,1609,1675,1753,1834,2005,2085,2150", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "870,947,1039,1135,1217,1295,1378,1460,1538,1604,1670,1748,1829,1899,2080,2145,2261"}}]}]}