@echo off
echo ========================================
echo  Nothing Phone One Camera App Build
echo ========================================

echo.
echo Step 1: Cleaning previous build...
call gradlew clean

echo.
echo Step 2: Syncing dependencies...
call gradlew --refresh-dependencies

echo.
echo Step 3: Building debug APK...
call gradlew assembleDebug

echo.
echo Step 4: Checking for build success...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ BUILD SUCCESSFUL!
    echo APK location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Ready to install on Nothing Phone One:
    echo adb install app\build\outputs\apk\debug\app-debug.apk
) else (
    echo ❌ BUILD FAILED!
    echo Check the error messages above.
)

echo.
echo ========================================
echo  Build process completed
echo ========================================
pause
