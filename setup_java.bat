@echo off
echo ========================================
echo  Java Environment Setup for VS Code
echo ========================================

echo.
echo Searching for Java installations...

REM Check Android Studio JBR locations
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
    echo ✅ Found Android Studio JBR: %JAVA_HOME%
    goto :found
)

if exist "C:\Program Files (x86)\Android\Android Studio\jbr\bin\java.exe" (
    set JAVA_HOME=C:\Program Files (x86)\Android\Android Studio\jbr
    echo ✅ Found Android Studio JBR: %JAVA_HOME%
    goto :found
)

if exist "C:\Program Files\Android\Android Studio\jre\bin\java.exe" (
    set JAVA_HOME=C:\Program Files\Android\Android Studio\jre
    echo ✅ Found Android Studio JRE: %JAVA_HOME%
    goto :found
)

REM Check for standalone JDK installations
for /d %%i in ("C:\Program Files\Java\jdk*") do (
    if exist "%%i\bin\java.exe" (
        set JAVA_HOME=%%i
        echo ✅ Found JDK: %JAVA_HOME%
        goto :found
    )
)

for /d %%i in ("C:\Program Files (x86)\Java\jdk*") do (
    if exist "%%i\bin\java.exe" (
        set JAVA_HOME=%%i
        echo ✅ Found JDK: %JAVA_HOME%
        goto :found
    )
)

echo ❌ Java not found!
echo.
echo Please install one of these:
echo 1. Android Studio (includes JDK)
echo 2. JDK 11+ from https://adoptium.net/
echo.
pause
exit /b 1

:found
echo.
echo Setting environment variables...
set PATH=%JAVA_HOME%\bin;%PATH%

echo.
echo Testing Java...
"%JAVA_HOME%\bin\java.exe" -version

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Java setup successful!
    echo.
    echo Environment variables set:
    echo JAVA_HOME=%JAVA_HOME%
    echo PATH updated with Java bin directory
    echo.
    echo Now you can build your camera app:
    echo   gradlew assembleDebug
    echo.
    echo 💡 To make this permanent:
    echo 1. Windows + R → sysdm.cpl
    echo 2. Environment Variables
    echo 3. Add JAVA_HOME=%JAVA_HOME%
    echo 4. Add %%JAVA_HOME%%\bin to PATH
) else (
    echo ❌ Java test failed!
)

echo.
echo ========================================
pause
