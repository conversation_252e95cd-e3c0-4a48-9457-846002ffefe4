@echo off
echo ========================================
echo  Install Camera App on Nothing Phone One
echo ========================================

echo.
echo Checking for connected devices...
adb devices

echo.
echo Installing debug APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    adb install -r app\build\outputs\apk\debug\app-debug.apk
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ INSTALLATION SUCCESSFUL!
        echo.
        echo 📱 Your Nothing Phone One now has:
        echo    ✅ iPhone 15 Pro camera experience
        echo    ✅ 15x smooth zoom (0.5x to 15x)
        echo    ✅ Portrait mode (1x, 2x, 3x)
        echo    ✅ Glyph flash integration
        echo    ✅ 5-second night mode
        echo    ✅ Professional filters
        echo    ✅ Smooth 60fps performance
        echo.
        echo 🚀 Open the Camera app on your phone and enjoy!
    ) else (
        echo.
        echo ❌ INSTALLATION FAILED!
        echo.
        echo 🔧 Make sure:
        echo 1. USB Debugging is enabled
        echo 2. Phone is connected via USB
        echo 3. Allow USB debugging when prompted
    )
) else (
    echo.
    echo ❌ APK not found!
    echo Build the app first: vscode_build.bat
)

echo.
echo ========================================
pause
