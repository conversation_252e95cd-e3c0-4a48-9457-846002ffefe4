package com.example.camera

import android.content.Context
import android.graphics.*
import android.util.Log
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
// RenderScript removed for compatibility
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

class PortraitProcessor(private val context: Context) {
    
    private val filterProcessor = FilterProcessor(context)
    // RenderScript removed for compatibility
    
    // iPhone 15 Pro style portrait zoom levels
    val portraitZoomLevels = listOf(1f, 2f, 3f)
    
    fun processPortraitImage(
        bitmap: Bitmap,
        zoomLevel: Float,
        filter: FilterType = FilterType.VIVID,
        blurIntensity: Float = 0.7f
    ): Bitmap {
        return try {
            Log.d("PortraitProcessor", "Processing portrait at ${zoomLevel}x zoom")

            // Simplified portrait processing for better compatibility
            val filteredBitmap = if (filter != FilterType.NONE) {
                filterProcessor.applyFilterOptimized(bitmap, filter)
            } else {
                bitmap.copy(bitmap.config, false)
            }

            // Apply basic enhancement for zoom level
            val finalResult = enhanceForZoomLevel(filteredBitmap, zoomLevel)

            // Cleanup intermediate bitmaps
            if (filteredBitmap != bitmap && filteredBitmap != finalResult) {
                filteredBitmap.recycle()
            }

            finalResult

        } catch (e: Exception) {
            Log.e("PortraitProcessor", "Portrait processing failed", e)
            bitmap.copy(bitmap.config, false) // Return copy on error
        }
    }
    
    private suspend fun generateDepthMask(bitmap: Bitmap): Bitmap {
        return suspendCancellableCoroutine { continuation ->
            val inputImage = InputImage.fromBitmap(bitmap, 0)
            
            segmenter.process(inputImage)
                .addOnSuccessListener { segmentationMask ->
                    val depthBitmap = createDepthBitmap(segmentationMask, bitmap.width, bitmap.height)
                    continuation.resume(depthBitmap)
                }
                .addOnFailureListener { exception ->
                    Log.e("PortraitProcessor", "Segmentation failed", exception)
                    // Create fallback center-focused mask
                    val fallbackMask = createFallbackDepthMask(bitmap.width, bitmap.height)
                    continuation.resume(fallbackMask)
                }
        }
    }
    
    private fun createDepthBitmap(mask: SegmentationMask, width: Int, height: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint()
        
        // Convert segmentation mask to depth bitmap
        val maskBuffer = mask.buffer
        val maskArray = ByteArray(maskBuffer.remaining())
        maskBuffer.get(maskArray)
        
        for (y in 0 until height) {
            for (x in 0 until width) {
                val index = y * width + x
                if (index < maskArray.size) {
                    val confidence = maskArray[index].toInt() and 0xFF
                    // Higher confidence = closer to camera = less blur
                    val alpha = (confidence * 255 / 255).coerceIn(0, 255)
                    paint.color = Color.argb(alpha, 255, 255, 255)
                    canvas.drawPoint(x.toFloat(), y.toFloat(), paint)
                }
            }
        }
        
        return bitmap
    }
    
    private fun createFallbackDepthMask(width: Int, height: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint()
        
        // Create center-focused radial gradient
        val centerX = width / 2f
        val centerY = height / 2f
        val radius = kotlin.math.min(width, height) / 3f
        
        val gradient = RadialGradient(
            centerX, centerY, radius,
            intArrayOf(Color.WHITE, Color.TRANSPARENT),
            floatArrayOf(0f, 1f),
            Shader.TileMode.CLAMP
        )
        
        paint.shader = gradient
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)
        
        return bitmap
    }
    
    private fun applyNaturalPortraitBlur(
        bitmap: Bitmap,
        depthMask: Bitmap,
        zoomLevel: Float,
        blurIntensity: Float
    ): Bitmap {
        // Adjust blur based on zoom level (closer zoom = more blur)
        val adjustedBlurRadius = when (zoomLevel) {
            1f -> (blurIntensity * 15f).toInt() // 1x: moderate blur
            2f -> (blurIntensity * 20f).toInt() // 2x: more blur
            3f -> (blurIntensity * 25f).toInt() // 3x: maximum blur
            else -> (blurIntensity * 15f).toInt()
        }.coerceIn(5, 25)
        
        return try {
            // Use CPU-based blur for compatibility (RenderScript removed)
            applyFallbackBlur(bitmap, depthMask, adjustedBlurRadius)
        } catch (e: Exception) {
            Log.e("PortraitProcessor", "Portrait blur failed", e)
            bitmap.copy(bitmap.config, false) // Return copy on error
        }
    }
    
    private fun compositeWithDepthMask(
        original: Bitmap,
        blurred: Bitmap,
        depthMask: Bitmap
    ): Bitmap {
        val result = Bitmap.createBitmap(original.width, original.height, original.config)
        val canvas = Canvas(result)
        val paint = Paint()
        
        // Draw blurred background
        canvas.drawBitmap(blurred, 0f, 0f, paint)
        
        // Use depth mask to reveal sharp foreground
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
        canvas.drawBitmap(depthMask, 0f, 0f, paint)
        
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OVER)
        canvas.drawBitmap(original, 0f, 0f, paint)
        
        return result
    }
    
    private fun applyFallbackBlur(bitmap: Bitmap, depthMask: Bitmap, radius: Int): Bitmap {
        // CPU-based blur fallback for better compatibility
        val result = bitmap.copy(bitmap.config, true)

        try {
            // Simple box blur implementation
            val pixels = IntArray(bitmap.width * bitmap.height)
            bitmap.getPixels(pixels, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)

            // Apply basic blur (simplified for performance)
            val blurredPixels = applyBoxBlur(pixels, bitmap.width, bitmap.height, radius)
            result.setPixels(blurredPixels, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)

        } catch (e: Exception) {
            Log.e("PortraitProcessor", "Fallback blur failed", e)
        }

        return result
    }

    private fun applyBoxBlur(pixels: IntArray, width: Int, height: Int, radius: Int): IntArray {
        // Simple box blur implementation for fallback
        val result = pixels.copyOf()
        val kernelSize = radius * 2 + 1

        // Horizontal pass
        for (y in 0 until height) {
            for (x in 0 until width) {
                var r = 0
                var g = 0
                var b = 0
                var count = 0

                for (kx in -radius..radius) {
                    val px = (x + kx).coerceIn(0, width - 1)
                    val pixel = pixels[y * width + px]

                    r += (pixel shr 16) and 0xFF
                    g += (pixel shr 8) and 0xFF
                    b += pixel and 0xFF
                    count++
                }

                val avgR = r / count
                val avgG = g / count
                val avgB = b / count
                val alpha = (pixels[y * width + x] shr 24) and 0xFF

                result[y * width + x] = (alpha shl 24) or (avgR shl 16) or (avgG shl 8) or avgB
            }
        }

        return result
    }
    
    private fun enhanceForZoomLevel(bitmap: Bitmap, zoomLevel: Float): Bitmap {
        val colorMatrix = ColorMatrix()
        
        when (zoomLevel) {
            1f -> {
                // 1x: Natural look with slight enhancement
                colorMatrix.setSaturation(1.1f)
            }
            2f -> {
                // 2x: Enhanced contrast for better subject separation
                colorMatrix.setSaturation(1.15f)
                val contrastMatrix = ColorMatrix(floatArrayOf(
                    1.1f, 0.0f, 0.0f, 0.0f, 0f,
                    0.0f, 1.1f, 0.0f, 0.0f, 0f,
                    0.0f, 0.0f, 1.1f, 0.0f, 0f,
                    0.0f, 0.0f, 0.0f, 1.0f, 0.0f
                ))
                colorMatrix.postConcat(contrastMatrix)
            }
            3f -> {
                // 3x: Maximum enhancement for dramatic portraits
                colorMatrix.setSaturation(1.2f)
                val dramaticMatrix = ColorMatrix(floatArrayOf(
                    1.15f, 0.0f, 0.0f, 0.0f, 5f,
                    0.0f, 1.15f, 0.0f, 0.0f, 5f,
                    0.0f, 0.0f, 1.15f, 0.0f, 5f,
                    0.0f, 0.0f, 0.0f, 1.0f, 0.0f
                ))
                colorMatrix.postConcat(dramaticMatrix)
            }
        }
        
        return filterProcessor.applyColorMatrix(bitmap, colorMatrix)
    }
    
    fun getOptimalPortraitResolution(zoomLevel: Float): android.util.Size {
        return when (zoomLevel) {
            1f -> android.util.Size(3000, 4000) // 1x: Full resolution
            2f -> android.util.Size(2400, 3200) // 2x: Slightly cropped
            3f -> android.util.Size(2000, 2667) // 3x: More cropped for telephoto effect
            else -> android.util.Size(3000, 4000)
        }
    }
    
    fun cleanup() {
        // RenderScript removed - no cleanup needed
        filterProcessor.cleanup()
    }
}

// Extension function to make ColorMatrix accessible
fun FilterProcessor.applyColorMatrix(bitmap: Bitmap, colorMatrix: ColorMatrix): Bitmap {
    val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
    val canvas = Canvas(result)
    val paint = Paint().apply {
        colorFilter = ColorMatrixColorFilter(colorMatrix)
    }
    canvas.drawBitmap(bitmap, 0f, 0f, paint)
    return result
}
