{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,407,523,600,695,808,945,1059,1200,1280,1378,1469,1565,1676,1799,1902,2043,2183,2325,2513,2647,2764,2885,3007,3099,3192,3313,3447,3547,3654,3755,3896,4043,4149,4249,4331,4408,4494,4577,4674,4750,4830,4927,5029,5118,5214,5298,5406,5503,5603,5718,5794,5894", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "167,283,402,518,595,690,803,940,1054,1195,1275,1373,1464,1560,1671,1794,1897,2038,2178,2320,2508,2642,2759,2880,3002,3094,3187,3308,3442,3542,3649,3750,3891,4038,4144,4244,4326,4403,4489,4572,4669,4745,4825,4922,5024,5113,5209,5293,5401,5498,5598,5713,5789,5889,5981"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2848,2965,3081,3200,4226,4303,4398,4511,4648,4762,4903,4983,5081,5172,5268,5379,5502,5605,5746,5886,6028,6216,6350,6467,6588,6710,6802,6895,7016,7150,7250,7357,7458,7599,7746,7852,8148,8316,9063,9310,9494,9861,9937,10017,10114,10216,10305,10401,10485,10593,10690,10790,10905,10981,11081", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "2960,3076,3195,3311,4298,4393,4506,4643,4757,4898,4978,5076,5167,5263,5374,5497,5600,5741,5881,6023,6211,6345,6462,6583,6705,6797,6890,7011,7145,7245,7352,7453,7594,7741,7847,7947,8225,8388,9144,9388,9586,9932,10012,10109,10211,10300,10396,10480,10588,10685,10785,10900,10976,11076,11168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,906,998,1091,1188,1282,1382,1476,1572,1667,1759,1851,1935,2042,2153,2255,2363,2471,2578,2749,2848", "endColumns": "107,105,106,88,100,123,84,80,91,92,96,93,99,93,95,94,91,91,83,106,110,101,107,107,106,170,98,84", "endOffsets": "208,314,421,510,611,735,820,901,993,1086,1183,1277,1377,1471,1567,1662,1754,1846,1930,2037,2148,2250,2358,2466,2573,2744,2843,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,906,998,1091,1188,1282,1382,1476,1572,1667,1759,1851,1935,2042,2153,2255,2363,2471,2578,2749,9149", "endColumns": "107,105,106,88,100,123,84,80,91,92,96,93,99,93,95,94,91,91,83,106,110,101,107,107,106,170,98,84", "endOffsets": "208,314,421,510,611,735,820,901,993,1086,1183,1277,1377,1471,1567,1662,1754,1846,1930,2037,2148,2250,2358,2466,2573,2744,2843,9229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4048,4143,7952,8049,8230,8393,8472,8569,8660,8747,8819,8888,8973,9234,9591,9667,9739", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "4138,4221,8044,8143,8311,8467,8564,8655,8742,8814,8883,8968,9058,9305,9662,9734,9856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3316,3413,3515,3614,3714,3821,3927,9393", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3408,3510,3609,3709,3816,3922,4043,9489"}}]}]}