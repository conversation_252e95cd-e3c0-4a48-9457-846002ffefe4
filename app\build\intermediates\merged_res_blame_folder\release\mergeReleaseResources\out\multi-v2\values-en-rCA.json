{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4070,4146,4229,4312,4410,4486,4566,4663,4760,4856,4951,5035,5137,5234,5333,5445,5521,5617", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,71,75,82,82,97,75,79,96,96,95,94,83,101,96,98,111,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4065,4141,4224,4307,4405,4481,4561,4658,4755,4851,4946,5030,5132,5229,5328,5440,5516,5612,5703"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2764,2878,2990,3097,4099,4177,4268,4377,4509,4621,4753,4833,4928,5015,5108,5223,5344,5444,5567,5686,5810,5968,6085,6197,6317,6439,6527,6621,6734,6854,6947,7045,7143,7268,7403,7505,7792,7950,8660,8898,9082,9443,9519,9599,9696,9793,9889,9984,10068,10170,10267,10366,10478,10554,10650", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,71,75,82,82,97,75,79,96,96,95,94,83,101,96,98,111,75,95,90", "endOffsets": "2873,2985,3092,3202,4172,4263,4372,4504,4616,4748,4828,4923,5010,5103,5218,5339,5439,5562,5681,5805,5963,6080,6192,6312,6434,6522,6616,6729,6849,6942,7040,7138,7263,7398,7500,7594,7859,8021,8738,8976,9175,9514,9594,9691,9788,9884,9979,10063,10165,10262,10361,10473,10549,10645,10736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1890,1993,2097,2196,2301,2404,2508,2664,2764", "endColumns": "103,99,107,83,99,114,76,75,90,92,95,93,100,92,94,93,90,90,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1885,1988,2092,2191,2296,2399,2503,2659,2759,2841"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1890,1993,2097,2196,2301,2404,2508,2664,8743", "endColumns": "103,99,107,83,99,114,76,75,90,92,95,93,100,92,94,93,90,90,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1885,1988,2092,2191,2296,2399,2503,2659,2759,8820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,558,640,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "192,274,368,467,553,635,725,814,898,963,1027,1105,1187,1260,1337,1403,1523"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3925,4017,7599,7693,7864,8026,8108,8198,8287,8371,8436,8500,8578,8825,9180,9257,9323", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "4012,4094,7688,7787,7945,8103,8193,8282,8366,8431,8495,8573,8655,8893,9252,9318,9438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3207,3303,3405,3504,3603,3707,3809,8981", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "3298,3400,3499,3598,3702,3804,3920,9077"}}]}]}