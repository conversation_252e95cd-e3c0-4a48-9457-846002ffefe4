{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,404,521,596,686,794,931,1046,1187,1268,1364,1455,1549,1664,1786,1887,2019,2150,2280,2444,2566,2686,2811,2932,3024,3118,3244,3374,3467,3565,3670,3806,3949,4054,4149,4230,4307,4397,4479,4584,4668,4747,4840,4937,5026,5125,5209,5310,5403,5499,5633,5719,5815", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "166,283,399,516,591,681,789,926,1041,1182,1263,1359,1450,1544,1659,1781,1882,2014,2145,2275,2439,2561,2681,2806,2927,3019,3113,3239,3369,3462,3560,3665,3801,3944,4049,4144,4225,4302,4392,4474,4579,4663,4742,4835,4932,5021,5120,5204,5305,5398,5494,5628,5714,5810,5898"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2816,2932,3049,3165,4200,4275,4365,4473,4610,4725,4866,4947,5043,5134,5228,5343,5465,5566,5698,5829,5959,6123,6245,6365,6490,6611,6703,6797,6923,7053,7146,7244,7349,7485,7628,7733,8021,8192,8931,9178,9361,9733,9817,9896,9989,10086,10175,10274,10358,10459,10552,10648,10782,10868,10964", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "2927,3044,3160,3277,4270,4360,4468,4605,4720,4861,4942,5038,5129,5223,5338,5460,5561,5693,5824,5954,6118,6240,6360,6485,6606,6698,6792,6918,7048,7141,7239,7344,7480,7623,7728,7823,8097,8264,9016,9255,9461,9812,9891,9984,10081,10170,10269,10353,10454,10547,10643,10777,10863,10959,11047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,889,980,1072,1167,1261,1362,1455,1550,1645,1736,1827,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,82,90,91,94,93,100,92,94,94,90,90,84,103,111,100,104,113,101,168,96,83", "endOffsets": "205,300,407,493,597,716,801,884,975,1067,1162,1256,1357,1450,1545,1640,1731,1822,1907,2011,2123,2224,2329,2443,2545,2714,2811,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,889,980,1072,1167,1261,1362,1455,1550,1645,1736,1827,1912,2016,2128,2229,2334,2448,2550,2719,9021", "endColumns": "104,94,106,85,103,118,84,82,90,91,94,93,100,92,94,94,90,90,84,103,111,100,104,113,101,168,96,83", "endOffsets": "205,300,407,493,597,716,801,884,975,1067,1162,1256,1357,1450,1545,1640,1731,1822,1907,2011,2123,2224,2329,2443,2545,2714,2811,9100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3282,3380,3487,3584,3683,3787,3891,9260", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3375,3482,3579,3678,3782,3886,4003,9356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4008,4113,7828,7922,8102,8269,8348,8441,8536,8621,8693,8764,8845,9105,9466,9545,9615", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4108,4195,7917,8016,8187,8343,8436,8531,8616,8688,8759,8840,8926,9173,9540,9610,9728"}}]}]}