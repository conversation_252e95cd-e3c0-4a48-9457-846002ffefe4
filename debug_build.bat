@echo off
echo ========================================
echo  Debug Build - Nothing Phone One Camera
echo ========================================

echo.
echo Step 1: Checking Java environment...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java not found! Please install JDK 11+ or set JAVA_HOME
    echo.
    echo Quick fix:
    echo 1. Open Android Studio
    echo 2. File → Project Structure → SDK Location
    echo 3. Copy JDK path (usually: C:\Program Files\Android\Android Studio\jbr)
    echo 4. Set JAVA_HOME environment variable to that path
    pause
    exit /b 1
)

echo.
echo Step 2: Cleaning build cache...
call gradlew clean --stacktrace

echo.
echo Step 3: Checking for compilation errors...
call gradlew compileDebugKotlin --stacktrace

echo.
echo Step 4: Full build attempt...
call gradlew assembleDebug --stacktrace

echo.
echo ========================================
echo  Build Debug Complete
echo ========================================
pause
