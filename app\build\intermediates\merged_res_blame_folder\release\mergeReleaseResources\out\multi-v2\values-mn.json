{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3245,3343,3445,3546,3644,3749,3861,9194", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3338,3440,3541,3639,3744,3856,3975,9290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,975,1046,1129,1212,1285,1362,1428", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,970,1041,1124,1207,1280,1357,1423,1540"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3980,4072,7789,7881,8056,8215,8301,8395,8482,8563,8636,8707,8790,9034,9392,9469,9535", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "4067,4153,7876,7972,8134,8296,8390,8477,8558,8631,8702,8785,8868,9102,9464,9530,9647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,390,503,580,673,784,922,1036,1171,1252,1350,1438,1532,1646,1764,1867,2005,2145,2273,2445,2567,2684,2801,2918,3007,3103,3222,3356,3451,3555,3657,3796,3937,4040,4134,4213,4289,4370,4457,4554,4630,4709,4804,4900,4991,5089,5172,5276,5371,5471,5598,5674,5774", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "165,277,385,498,575,668,779,917,1031,1166,1247,1345,1433,1527,1641,1759,1862,2000,2140,2268,2440,2562,2679,2796,2913,3002,3098,3217,3351,3446,3550,3652,3791,3932,4035,4129,4208,4284,4365,4452,4549,4625,4704,4799,4895,4986,5084,5167,5271,5366,5466,5593,5669,5769,5860"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,2912,3024,3132,4158,4235,4328,4439,4577,4691,4826,4907,5005,5093,5187,5301,5419,5522,5660,5800,5928,6100,6222,6339,6456,6573,6662,6758,6877,7011,7106,7210,7312,7451,7592,7695,7977,8139,8873,9107,9295,9652,9728,9807,9902,9998,10089,10187,10270,10374,10469,10569,10696,10772,10872", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "2907,3019,3127,3240,4230,4323,4434,4572,4686,4821,4902,5000,5088,5182,5296,5414,5517,5655,5795,5923,6095,6217,6334,6451,6568,6657,6753,6872,7006,7101,7205,7307,7446,7587,7690,7784,8051,8210,8949,9189,9387,9723,9802,9897,9993,10084,10182,10265,10369,10464,10564,10691,10767,10867,10958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,899,990,1082,1177,1273,1371,1464,1558,1650,1741,1831,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,81,90,91,94,95,97,92,93,91,90,89,78,106,102,96,106,101,112,158,98,79", "endOffsets": "214,314,423,509,615,729,812,894,985,1077,1172,1268,1366,1459,1553,1645,1736,1826,1905,2012,2115,2212,2319,2421,2534,2693,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,899,990,1082,1177,1273,1371,1464,1558,1650,1741,1831,1910,2017,2120,2217,2324,2426,2539,2698,8954", "endColumns": "113,99,108,85,105,113,82,81,90,91,94,95,97,92,93,91,90,89,78,106,102,96,106,101,112,158,98,79", "endOffsets": "214,314,423,509,615,729,812,894,985,1077,1172,1268,1366,1459,1553,1645,1736,1826,1905,2012,2115,2212,2319,2421,2534,2693,2792,9029"}}]}]}