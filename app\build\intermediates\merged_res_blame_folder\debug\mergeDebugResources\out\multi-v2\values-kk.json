{"logs": [{"outputFile": "com.example.camera.app-mergeDebugResources-52:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,394,506,581,678,793,927,1048,1169,1249,1343,1433,1526,1638,1758,1862,2000,2136,2264,2419,2538,2649,2765,2877,2964,3058,3177,3308,3408,3515,3617,3751,3892,3996,4091,4173,4248,4330,4410,4510,4595,4676,4773,4873,4966,5058,5141,5241,5336,5429,5562,5648,5755", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "164,278,389,501,576,673,788,922,1043,1164,1244,1338,1428,1521,1633,1753,1857,1995,2131,2259,2414,2533,2644,2760,2872,2959,3053,3172,3303,3403,3510,3612,3746,3887,3991,4086,4168,4243,4325,4405,4505,4590,4671,4768,4868,4961,5053,5136,5236,5331,5424,5557,5643,5750,5847"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2796,2910,3024,3135,4137,4212,4309,4424,4558,4679,4800,4880,4974,5064,5157,5269,5389,5493,5631,5767,5895,6050,6169,6280,6396,6508,6595,6689,6808,6939,7039,7146,7248,7382,7523,7627,7929,8098,8824,9060,9241,9605,9690,9771,9868,9968,10061,10153,10236,10336,10431,10524,10657,10743,10850", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "2905,3019,3130,3242,4207,4304,4419,4553,4674,4795,4875,4969,5059,5152,5264,5384,5488,5626,5762,5890,6045,6164,6275,6391,6503,6590,6684,6803,6934,7034,7141,7243,7377,7518,7622,7717,8006,8168,8901,9135,9336,9685,9766,9863,9963,10056,10148,10231,10331,10426,10519,10652,10738,10845,10942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3247,3342,3444,3546,3649,3753,3850,9140", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3337,3439,3541,3644,3748,3845,3956,9236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,8906", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,8982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,990,1058,1141,1226,1299,1375,1445", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,985,1053,1136,1221,1294,1370,1440,1558"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3961,4054,7722,7827,8011,8173,8254,8347,8437,8519,8588,8656,8739,8987,9341,9417,9487", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "4049,4132,7822,7924,8093,8249,8342,8432,8514,8583,8651,8734,8819,9055,9412,9482,9600"}}]}]}