# iPhone-Style Camera App for Nothing Phone One

A professional camera application designed specifically for the Nothing Phone One, featuring iPhone-like design and functionality optimized for the Snapdragon 778G+ processor and dual camera system.

## Features

### 🎯 Optimized for Nothing Phone One
- **Sony IMX 766 50MP Main Camera** - 1/1.56" sensor with f/1.88 OIS lens
- **Samsung JN1 50MP Ultrawide** - 1/2.76" sensor with f/2.2 lens and macro capability
- **Snapdragon 778G+ Optimizations** - GPU acceleration and AI enhancements
- **Glyph Interface Integration** - Visual feedback through Nothing Phone's unique Glyph interface

### 📸 Camera Modes
- **Photo Mode** - High-quality 12.5MP photos (binned from 50MP for better low light)
- **Video Mode** - 4K@30fps and 1080p@60fps recording
- **Portrait Mode** - Depth-based background blur
- **Cinematic Mode** - Professional video with depth effects
- **Macro Mode** - Close-up photography using ultrawide camera (4cm minimum focus)
- **Night Mode** - Automatic detection and enhanced low-light photography

### 🎨 iPhone-Style Filters
- **Vivid** - Enhanced saturation and warmth (default)
- **Dramatic** - High contrast with enhanced shadows
- **Brilliant** - Bright with enhanced highlights
- **Mono** - Classic black and white
- **Silvertone** - Black and white with blue tint
- **Noir** - High contrast black and white

### 🔧 Advanced Features
- **10x Digital Zoom** - Smooth zoom with haptic feedback
- **Automatic Night Mode Detection** - Smart scene analysis
- **PDAF (Phase Detection Auto Focus)** - Fast and accurate focusing
- **OIS (Optical Image Stabilization)** - Steady shots and videos
- **Real-time Filter Preview** - See effects before capture
- **Haptic Feedback** - iPhone-like tactile responses

## Technical Specifications

### Camera Hardware Support
- **Main Camera**: Sony IMX 766, 50MP, 1/1.56", f/1.88, OIS, PDAF
- **Ultrawide Camera**: Samsung JN1, 50MP, 1/2.76", f/2.2, 4cm macro focus
- **Default Output**: 12.5MP (4-to-1 pixel binning for better low light)
- **Video**: 4K@30fps, 1080p@60fps with stabilization

### Performance Optimizations
- **Snapdragon 778G+ ISP** - Hardware-accelerated image processing
- **GPU Acceleration** - Filter processing using Adreno 642L
- **AI Enhancement** - Hexagon DSP for scene detection
- **Memory Management** - Optimized for 8GB/12GB RAM variants

## Installation

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 34
- Nothing Phone One running Android 12+
- Minimum 4GB free storage

### Build Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Camera
   ```

2. **Open in Android Studio**
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to the cloned directory

3. **Sync dependencies**
   ```bash
   ./gradlew build
   ```

4. **Install on Nothing Phone One**
   ```bash
   ./gradlew installDebug
   ```

## Configuration for Nothing Phone One

### Camera Permissions
The app automatically requests the following permissions:
- `CAMERA` - Camera access
- `RECORD_AUDIO` - Video recording with audio
- `READ_MEDIA_IMAGES` - Gallery access (Android 13+)
- `READ_MEDIA_VIDEO` - Video gallery access
- `VIBRATE` - Haptic feedback

### Performance Settings
For optimal performance on Nothing Phone One:

1. **Enable High Performance Mode**
   - Settings > Battery > Performance Mode > High Performance

2. **Camera App Optimization**
   - Settings > Apps > Camera > Battery > Unrestricted

3. **Storage Optimization**
   - Ensure at least 2GB free space for optimal performance

## Usage Guide

### Basic Operation
1. **Launch the app** - Grant camera permissions when prompted
2. **Select mode** - Swipe horizontally on mode selector
3. **Adjust zoom** - Pinch to zoom (1x to 10x)
4. **Apply filters** - Tap filter button in top bar
5. **Capture** - Tap the white shutter button

### Advanced Features

#### Night Mode
- Automatically detected in low light
- Moon icon appears when conditions are suitable
- Tap moon icon to activate 4-5 second exposure

#### Macro Mode
- Available when zoom is at 1x or lower
- Tap macro icon (🔍) to enable
- Switches to ultrawide camera for close-up shots

#### Cinematic Video
- Select "CINEMATIC" mode
- Automatic depth detection for background blur
- Professional-grade video recording

### Filter Customization
The Vivid filter is specifically tuned for Nothing Phone One's display and camera characteristics:
- Enhanced for P3 wide color gamut
- Optimized for Sony IMX 766 color science
- Balanced for Nothing Phone One's OLED display

## Troubleshooting

### Common Issues

**App crashes on startup**
- Ensure all permissions are granted
- Restart the app
- Clear app cache if needed

**Poor image quality**
- Check available storage (need 2GB+ free)
- Ensure good lighting conditions
- Clean camera lens

**Slow performance**
- Close other camera apps
- Enable high performance mode
- Restart device if needed

**Glyph interface not working**
- Feature is simulated in current version
- Full Glyph integration pending official SDK

### Performance Tips
1. Use Photo mode for best quality
2. Enable OIS for handheld shots
3. Use Night mode in low light
4. Clean lens regularly for optimal results
5. Keep app updated for latest optimizations

## Development

### Architecture
- **MVVM Pattern** - Clean separation of concerns
- **Jetpack Compose** - Modern UI framework
- **CameraX** - Google's camera library
- **Coroutines** - Asynchronous operations
- **Material 3** - Modern design system

### Key Components
- `CameraController` - Main camera logic
- `FilterProcessor` - Image filter processing
- `NothingPhoneOptimizations` - Device-specific optimizations
- `GlyphController` - Nothing Phone Glyph integration

### Contributing
1. Fork the repository
2. Create feature branch
3. Test on Nothing Phone One
4. Submit pull request

## License
This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments
- Nothing Phone One camera specifications
- Snapdragon 778G+ optimization guidelines
- iPhone camera UI/UX inspiration
- CameraX documentation and best practices
