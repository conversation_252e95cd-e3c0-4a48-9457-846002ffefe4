package com.example.camera

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.media.Image
import android.net.Uri
import android.os.Environment
import android.util.Log
import android.util.Size
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.*
import androidx.camera.video.VideoCapture
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlinx.coroutines.delay

enum class FlashMode {
    OFF, ON, AUTO, GLYPH
}

enum class CameraMode {
    PHOTO, VIDEO, PORTRAIT, CINEMATIC
}

class CameraController(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner
) {
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageCapture: ImageCapture? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    private var imageAnalyzer: ImageAnalysis? = null
    
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    private val filterProcessor = FilterProcessor(context)
    private val glyphController = GlyphController(context)
    private val portraitProcessor = PortraitProcessor(context)
    private val videoController = VideoController(context, glyphController)
    private val optimizations = NothingPhoneOptimizations(context)
    
    // Camera configuration optimized for Nothing Phone One
    private var currentCameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
    private var currentFilter = FilterType.VIVID
    private var isNightModeEnabled = false
    private var isMacroModeEnabled = false

    // Flash and Glyph integration states
    private var flashMode = FlashMode.OFF
    private var isGlyphFlashEnabled = false

    // iPhone 15 Pro style zoom levels
    private val zoomLevels = listOf(0.5f, 1f, 2f, 3f, 5f, 10f, 15f)
    private val portraitZoomLevels = listOf(1f, 2f, 3f) // Portrait mode specific
    private var currentZoomIndex = 1 // Start at 1x
    private var currentZoom = 1f
    private var currentMode = CameraMode.PHOTO

    // Nothing Phone One specific camera IDs
    private val mainCameraId = "0" // Sony IMX 766 50MP
    private val ultraWideCameraId = "1" // Samsung JN1 50MP
    
    suspend fun initializeCamera(): Boolean {
        return withContext(Dispatchers.Main) {
            try {
                Log.d("CameraController", "Starting camera initialization...")
                cameraProvider = ProcessCameraProvider.getInstance(context).get()
                setupCamera()
                Log.d("CameraController", "Camera initialization successful")
                true
            } catch (e: Exception) {
                Log.e("CameraController", "Camera initialization failed", e)
                false
            }
        }
    }
    
    private fun setupCamera() {
        val cameraProvider = this.cameraProvider ?: return

        // Use optimized configurations for Nothing Phone One
        preview = optimizations.getOptimizedPreviewConfig()
        imageCapture = optimizations.getOptimizedImageCaptureConfig()
        videoCapture = videoController.initializeVideoCapture()

        // Configure image analysis for real-time processing (lightweight)
        imageAnalyzer = ImageAnalysis.Builder()
            .setTargetResolution(Size(640, 480)) // Reduced for better performance
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()

        bindCameraUseCases()
    }
    
    private fun bindCameraUseCases() {
        val cameraProvider = this.cameraProvider ?: return
        
        try {
            cameraProvider.unbindAll()
            
            camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                currentCameraSelector,
                preview,
                imageCapture,
                videoCapture,
                imageAnalyzer
            )
            
            // Enable auto-focus and exposure
            camera?.cameraControl?.let { control ->
                // Set initial zoom to 1x
                control.setZoomRatio(1.0f)
            }
            
        } catch (e: Exception) {
            Log.e("CameraController", "Use case binding failed", e)
        }
    }
    
    fun setSurfaceProvider(surfaceProvider: Preview.SurfaceProvider) {
        preview?.setSurfaceProvider(surfaceProvider)
    }
    
    fun setZoom(zoomRatio: Float) {
        currentZoom = zoomRatio.coerceIn(0.5f, 15f)
        camera?.cameraControl?.setZoomRatio(currentZoom)
    }

    // iPhone 15 Pro style smooth zoom to next level
    suspend fun smoothZoomToNext(): Float {
        if (currentZoomIndex < zoomLevels.size - 1) {
            currentZoomIndex++
            val targetZoom = zoomLevels[currentZoomIndex]
            smoothZoomTo(targetZoom)
            return targetZoom
        }
        return currentZoom
    }

    suspend fun smoothZoomToPrevious(): Float {
        if (currentZoomIndex > 0) {
            currentZoomIndex--
            val targetZoom = zoomLevels[currentZoomIndex]
            smoothZoomTo(targetZoom)
            return targetZoom
        }
        return currentZoom
    }

    suspend fun smoothZoomTo(targetZoom: Float) {
        val startZoom = currentZoom
        val steps = 15 // Reduced for better performance
        val stepSize = (targetZoom - startZoom) / steps

        // Handle video mode camera switching
        if (currentMode == CameraMode.VIDEO || currentMode == CameraMode.CINEMATIC) {
            videoController.smoothVideoZoom(targetZoom, camera)
        } else {
            // Regular smooth zoom for photo modes
            repeat(steps) { step ->
                val newZoom = startZoom + (stepSize * (step + 1))
                setZoom(newZoom)
                delay(16) // 60fps smooth animation
            }
        }
        currentZoom = targetZoom
    }

    fun getCurrentZoomLevel(): Float = currentZoom
    fun getAvailableZoomLevels(): List<Float> = when (currentMode) {
        CameraMode.PORTRAIT -> portraitZoomLevels
        else -> zoomLevels
    }

    fun setMode(mode: CameraMode) {
        currentMode = mode
        // Reset zoom to 1x when switching to portrait mode
        if (mode == CameraMode.PORTRAIT && currentZoom !in portraitZoomLevels) {
            currentZoom = 1f
            currentZoomIndex = portraitZoomLevels.indexOf(1f)
            camera?.cameraControl?.setZoomRatio(currentZoom)
        }
    }

    fun getCurrentMode(): CameraMode = currentMode
    
    fun switchCamera() {
        currentCameraSelector = if (currentCameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
            CameraSelector.DEFAULT_FRONT_CAMERA
        } else {
            CameraSelector.DEFAULT_BACK_CAMERA
        }
        bindCameraUseCases()
    }
    
    fun switchToUltraWide() {
        // Switch to ultrawide camera (Samsung JN1)
        currentCameraSelector = CameraSelector.Builder()
            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
            .build()
        bindCameraUseCases()
    }
    
    fun enableMacroMode(enabled: Boolean) {
        isMacroModeEnabled = enabled
        if (enabled) {
            switchToUltraWide() // Use ultrawide for macro (4cm close focus)
        }
    }
    
    fun setFilter(filter: FilterType) {
        currentFilter = filter
    }

    // iPhone 15 Pro style flash and Glyph integration
    fun toggleFlash(): FlashMode {
        flashMode = when (flashMode) {
            FlashMode.OFF -> {
                // Flash ON
                imageCapture?.flashMode = ImageCapture.FLASH_MODE_ON
                glyphController.setFlashMode(false)
                FlashMode.ON
            }
            FlashMode.ON -> {
                // Flash OFF, Glyph ON
                imageCapture?.flashMode = ImageCapture.FLASH_MODE_OFF
                glyphController.setFlashMode(true)
                isGlyphFlashEnabled = true
                FlashMode.GLYPH
            }
            FlashMode.GLYPH -> {
                // Both OFF
                imageCapture?.flashMode = ImageCapture.FLASH_MODE_OFF
                glyphController.setFlashMode(false)
                isGlyphFlashEnabled = false
                FlashMode.OFF
            }
            FlashMode.AUTO -> {
                imageCapture?.flashMode = ImageCapture.FLASH_MODE_AUTO
                glyphController.setFlashMode(false)
                FlashMode.AUTO
            }
        }
        return flashMode
    }

    fun getCurrentFlashMode(): FlashMode = flashMode
    fun isGlyphFlashActive(): Boolean = isGlyphFlashEnabled

    // Video recording functions
    fun startVideoRecording(): Boolean {
        return videoController.startVideoRecording()
    }

    fun stopVideoRecording(): Uri? {
        return videoController.stopVideoRecording()
    }

    fun isRecording(): Boolean {
        return videoController.isCurrentlyRecording()
    }
    
    suspend fun capturePhoto(): Uri? {
        return withContext(Dispatchers.IO) {
            try {
                val imageCapture = <EMAIL> ?: return@withContext null

                // Create output file
                val photoFile = createImageFile()
                val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

                // Trigger appropriate flash/Glyph effect
                when (flashMode) {
                    FlashMode.GLYPH -> glyphController.triggerCaptureFlash()
                    else -> glyphController.triggerCaptureEffect()
                }

                // Enhanced night mode capture for Sony IMX 766
                if (isNightModeEnabled) {
                    return@withContext captureNightModePhoto(photoFile)
                }

                // Portrait mode capture
                if (currentMode == CameraMode.PORTRAIT) {
                    return@withContext capturePortraitPhoto(photoFile)
                }

                // Regular capture
                var resultUri: Uri? = null
                imageCapture.takePicture(
                    outputOptions,
                    ContextCompat.getMainExecutor(context),
                    object : ImageCapture.OnImageSavedCallback {
                        override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                            resultUri = Uri.fromFile(photoFile)

                            // Apply filter if needed
                            if (currentFilter != FilterType.NONE) {
                                applyFilterToSavedImage(photoFile)
                            }
                        }

                        override fun onError(exception: ImageCaptureException) {
                            Log.e("CameraController", "Photo capture failed", exception)
                        }
                    }
                )

                // Wait for capture to complete
                delay(1000)
                resultUri

            } catch (e: Exception) {
                Log.e("CameraController", "Photo capture error", e)
                null
            }
        }
    }

    // Enhanced 5-second night mode for Sony IMX 766
    private suspend fun captureNightModePhoto(photoFile: File): Uri? {
        return try {
            Log.d("CameraController", "Starting 5-second night mode capture")

            // Configure for night mode - Sony IMX 766 optimized
            val nightModeCapture = ImageCapture.Builder()
                .setTargetResolution(Size(4000, 3000)) // 12.5MP for better low light
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .setFlashMode(ImageCapture.FLASH_MODE_OFF) // Use sensor capabilities
                .setJpegQuality(98) // Maximum quality for night shots
                .build()

            // Show Glyph progress ring during 5-second capture
            glyphController.startNightModeCapture()

            // Simulate 5-second exposure with progress updates
            repeat(50) { step ->
                val progress = step / 49f
                glyphController.setProgressRing(progress)
                delay(100) // 5 seconds total (50 * 100ms)
            }

            val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()
            var resultUri: Uri? = null

            nightModeCapture.takePicture(
                outputOptions,
                ContextCompat.getMainExecutor(context),
                object : ImageCapture.OnImageSavedCallback {
                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                        resultUri = Uri.fromFile(photoFile)

                        // Apply enhanced night mode processing
                        if (currentFilter != FilterType.NONE) {
                            applyNightModeFilter(photoFile)
                        }
                    }

                    override fun onError(exception: ImageCaptureException) {
                        Log.e("CameraController", "Night mode capture failed", exception)
                    }
                }
            )

            // Complete the capture
            glyphController.completeNightModeCapture()
            delay(500)
            resultUri

        } catch (e: Exception) {
            Log.e("CameraController", "Night mode capture error", e)
            glyphController.completeNightModeCapture()
            null
        }
    }

    // iPhone 15 Pro style portrait capture
    private suspend fun capturePortraitPhoto(photoFile: File): Uri? {
        return try {
            Log.d("CameraController", "Starting portrait capture at ${currentZoom}x zoom")

            // Configure for portrait mode with optimal resolution
            val portraitResolution = portraitProcessor.getOptimalPortraitResolution(currentZoom)
            val portraitCapture = ImageCapture.Builder()
                .setTargetResolution(portraitResolution)
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .setFlashMode(when (flashMode) {
                    FlashMode.ON -> ImageCapture.FLASH_MODE_ON
                    FlashMode.AUTO -> ImageCapture.FLASH_MODE_AUTO
                    else -> ImageCapture.FLASH_MODE_OFF
                })
                .setJpegQuality(95) // High quality for portraits
                .build()

            val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()
            var resultUri: Uri? = null

            // Capture the base image
            portraitCapture.takePicture(
                outputOptions,
                ContextCompat.getMainExecutor(context),
                object : ImageCapture.OnImageSavedCallback {
                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                        resultUri = Uri.fromFile(photoFile)

                        // Process portrait effect in background
                        cameraExecutor.execute {
                            applyPortraitProcessing(photoFile)
                        }
                    }

                    override fun onError(exception: ImageCaptureException) {
                        Log.e("CameraController", "Portrait capture failed", exception)
                    }
                }
            )

            delay(1500) // Wait for capture and initial processing
            resultUri

        } catch (e: Exception) {
            Log.e("CameraController", "Portrait capture error", e)
            null
        }
    }

    private fun applyPortraitProcessing(file: File) {
        try {
            val bitmap = BitmapFactory.decodeFile(file.absolutePath)

            // Apply portrait processing with current zoom and filter
            try {
                // Simplified portrait processing for better compatibility
                val filteredBitmap = if (currentFilter != FilterType.NONE) {
                    filterProcessor.applyFilterOptimized(bitmap, currentFilter)
                } else {
                    bitmap
                }

                // Save the processed portrait
                FileOutputStream(file).use { out ->
                    filteredBitmap.compress(Bitmap.CompressFormat.JPEG, 95, out)
                }

                bitmap.recycle()
                if (filteredBitmap != bitmap) filteredBitmap.recycle()

                Log.d("CameraController", "Portrait processing completed")

            } catch (e: Exception) {
                Log.e("CameraController", "Portrait processing failed", e)
            }

        } catch (e: Exception) {
            Log.e("CameraController", "Portrait processing setup failed", e)
        }
    }

    private fun applyFilterToSavedImage(file: File) {
        try {
            val bitmap = BitmapFactory.decodeFile(file.absolutePath)
            val filteredBitmap = filterProcessor.applyFilterOptimized(bitmap, currentFilter)

            FileOutputStream(file).use { out ->
                filteredBitmap.compress(Bitmap.CompressFormat.JPEG, 95, out)
            }

            bitmap.recycle()
            filteredBitmap.recycle()
        } catch (e: Exception) {
            Log.e("CameraController", "Filter application failed", e)
        }
    }

    private fun applyNightModeFilter(file: File) {
        try {
            val bitmap = BitmapFactory.decodeFile(file.absolutePath)

            // Enhanced night mode processing for Sony IMX 766
            val enhancedBitmap = filterProcessor.applyNightModeEnhancement(bitmap)
            val filteredBitmap = if (currentFilter != FilterType.NONE) {
                filterProcessor.applyFilterOptimized(enhancedBitmap, currentFilter)
            } else {
                enhancedBitmap
            }

            FileOutputStream(file).use { out ->
                filteredBitmap.compress(Bitmap.CompressFormat.JPEG, 98, out) // Higher quality for night shots
            }

            bitmap.recycle()
            if (enhancedBitmap != filteredBitmap) enhancedBitmap.recycle()
            filteredBitmap.recycle()
        } catch (e: Exception) {
            Log.e("CameraController", "Night mode filter application failed", e)
        }
    }
    
    private fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "IMG_${timeStamp}"
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(imageFileName, ".jpg", storageDir)
    }
    
    fun enableNightMode(enabled: Boolean) {
        isNightModeEnabled = enabled
        // Configure camera for night mode
        camera?.cameraControl?.let { control ->
            if (enabled) {
                // Increase exposure time and ISO for night mode
                // This would require Camera2 API for full control
            }
        }
    }
    
    fun detectNightModeConditions(): Boolean {
        // Implement light condition detection
        // Return true if night mode should be suggested
        return false // Placeholder
    }
    
    fun cleanup() {
        videoController.cleanup()
        cameraExecutor.shutdown()
        filterProcessor.cleanup()
        portraitProcessor.cleanup()
    }
}
