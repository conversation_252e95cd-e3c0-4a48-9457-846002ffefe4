{"logs": [{"outputFile": "com.example.camera.app-mergeDebugResources-52:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3108,3200,3300,3394,3490,3583,3676,8471", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3195,3295,3389,3485,3578,3671,3772,8567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3777,3863,7158,7247,7415,7572,7650,7728,7813,7888,7952,8016,8090,8323,8665,8741,8806", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "3858,3936,7242,7339,7493,7645,7723,7808,7883,7947,8011,8085,8161,8387,8736,8801,8918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,842,933,1025,1120,1214,1315,1408,1503,1597,1688,1779,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,75,90,91,94,93,100,92,94,93,90,90,76,101,97,94,102,95,95,147,96,77", "endOffsets": "197,290,395,477,575,683,761,837,928,1020,1115,1209,1310,1403,1498,1592,1683,1774,1851,1953,2051,2146,2249,2345,2441,2589,2686,2764"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,842,933,1025,1120,1214,1315,1408,1503,1597,1688,1779,1856,1958,2056,2151,2254,2350,2446,2594,8245", "endColumns": "96,92,104,81,97,107,77,75,90,91,94,93,100,92,94,93,90,90,76,101,97,94,102,95,95,147,96,77", "endOffsets": "197,290,395,477,575,683,761,837,928,1020,1115,1209,1310,1403,1498,1592,1683,1774,1851,1953,2051,2146,2249,2345,2441,2589,2686,8318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,265,370,472,547,633,737,852,960,1080,1158,1251,1332,1418,1521,1630,1728,1848,1966,2076,2199,2305,2402,2503,2606,2691,2782,2886,2994,3081,3174,3267,3384,3507,3602,3689,3760,3834,3913,3992,4085,4161,4239,4333,4424,4513,4606,4685,4777,4868,4961,5068,5144,5240", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "157,260,365,467,542,628,732,847,955,1075,1153,1246,1327,1413,1516,1625,1723,1843,1961,2071,2194,2300,2397,2498,2601,2686,2777,2881,2989,3076,3169,3262,3379,3502,3597,3684,3755,3829,3908,3987,4080,4156,4234,4328,4419,4508,4601,4680,4772,4863,4956,5063,5139,5235,5325"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2691,2798,2901,3006,3941,4016,4102,4206,4321,4429,4549,4627,4720,4801,4887,4990,5099,5197,5317,5435,5545,5668,5774,5871,5972,6075,6160,6251,6355,6463,6550,6643,6736,6853,6976,7071,7344,7498,8166,8392,8572,8923,8999,9077,9171,9262,9351,9444,9523,9615,9706,9799,9906,9982,10078", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "2793,2896,3001,3103,4011,4097,4201,4316,4424,4544,4622,4715,4796,4882,4985,5094,5192,5312,5430,5540,5663,5769,5866,5967,6070,6155,6246,6350,6458,6545,6638,6731,6848,6971,7066,7153,7410,7567,8240,8466,8660,8994,9072,9166,9257,9346,9439,9518,9610,9701,9794,9901,9977,10073,10163"}}]}]}