{"logs": [{"outputFile": "com.example.camera.test.app-mergeDebugAndroidTestResources-32:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1156b8a02ed3a004521710322c86df6f\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,925,1007,1105,1205,1291,1374,1465,1552,1637,1707,1774,1856,1939,2112,2190,2256", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "920,1002,1100,1200,1286,1369,1460,1547,1632,1702,1769,1851,1934,2006,2185,2251,2370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\adb64f38986e7cdd49a1ca36d676d869\\transformed\\core-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,399,496,602,720,2011", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "195,297,394,491,597,715,830,2107"}}]}]}