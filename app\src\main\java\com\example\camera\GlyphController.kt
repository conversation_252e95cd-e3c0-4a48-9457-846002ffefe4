package com.example.cameraxapp

import android.content.Context
import android.os.Build
import com.nothing.karn.Karn
import com.nothing.karn.information.LightsManager

class GlyphController(private val context: Context) {
    private val karn: Karn? = if (Build.VERSION.SDK_INT >= 30) Karn.from(context) else null
    private val lightsManager: LightsManager? = karn?.lightsManager

    fun triggerCaptureEffect() {
        lightsManager?.pulse()
    }

    fun setProgressRing(progress: Float) {
        // progress from 0.0f to 1.0f
        lightsManager?.setProgressRing(progress.coerceIn(0f, 1f))
    }
}