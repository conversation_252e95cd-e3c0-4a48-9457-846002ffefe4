@echo off
echo ========================================
echo  Nothing Phone Camera - VS Code Build
echo ========================================

echo.
echo Checking Java environment...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java not found! Setting up Android Studio JDK...
    set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
    set PATH=%JAVA_HOME%\bin;%PATH%
)

echo.
echo Step 1: Cleaning previous build...
call gradlew clean

echo.
echo Step 2: Building debug APK...
call gradlew assembleDebug --stacktrace

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo.
    echo ✅ BUILD SUCCESSFUL!
    echo.
    echo 📱 APK Location: app\build\outputs\apk\debug\app-debug.apk
    echo 📊 APK Size: 
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do echo    %%~zA bytes
    echo.
    echo 🚀 Install on Nothing Phone One:
    echo    adb install app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 🎯 Or use VS Code task:
    echo    Ctrl+Shift+P → "Tasks: Run Task" → "Install Debug on Device"
    echo.
    echo ✨ Your iPhone 15 Pro camera app is ready!
) else (
    echo.
    echo ❌ BUILD FAILED!
    echo Check the error messages above.
    echo.
    echo 🔧 Try these fixes:
    echo 1. Open VS Code and install required extensions
    echo 2. Check Java environment: java -version
    echo 3. Run: gradlew clean --refresh-dependencies
)

echo.
echo ========================================
echo  VS Code Build Complete
echo ========================================
pause
