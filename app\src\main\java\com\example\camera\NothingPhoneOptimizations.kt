package com.example.camera

import android.content.Context
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.hardware.camera2.CameraMetadata
import android.util.Log
import android.util.Size
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider

/**
 * Optimizations specifically for Nothing Phone One with Snapdragon 778G+
 * - Sony IMX 766 50MP main camera (1/1.56" sensor, f/1.88, OIS)
 * - Samsung JN1 50MP ultrawide (1/2.76" sensor, f/2.2, macro capable)
 */
class NothingPhoneOptimizations(private val context: Context) {
    
    private val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
    
    // Camera IDs for Nothing Phone One
    private val mainCameraId = "0" // Sony IMX 766
    private val ultraWideCameraId = "1" // Samsung JN1
    
    fun getOptimizedImageCaptureConfig(): ImageCapture {
        return ImageCapture.Builder()
            .setTargetResolution(getOptimalPhotoResolution())
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
            .setFlashMode(ImageCapture.FLASH_MODE_AUTO)
            .setJpegQuality(95) // High quality for Sony IMX 766
            .build()
    }
    
    fun getOptimizedPreviewConfig(): Preview {
        return Preview.Builder()
            .setTargetResolution(getOptimalPreviewResolution())
            .build()
    }
    
    fun getOptimizedVideoConfig(): VideoCapture<*>? {
        // Configure for cinematic video with Snapdragon 778G+ capabilities
        return null // Placeholder - would configure for 4K@30fps or 1080p@60fps
    }
    
    private fun getOptimalPhotoResolution(): Size {
        return try {
            val characteristics = cameraManager.getCameraCharacteristics(mainCameraId)
            val map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
            
            // Sony IMX 766 optimal resolutions
            val availableSizes = map?.getOutputSizes(android.graphics.ImageFormat.JPEG)
            
            // Prefer 12.5MP (binned from 50MP) for better low light performance
            availableSizes?.find { it.width == 4000 && it.height == 3000 }
                ?: Size(4000, 3000) // Default to 12.5MP
                
        } catch (e: Exception) {
            Log.e("NothingPhoneOpt", "Failed to get optimal photo resolution", e)
            Size(4000, 3000) // Safe default
        }
    }
    
    private fun getOptimalPreviewResolution(): Size {
        // Nothing Phone One display: 2400x1080, 120Hz
        return Size(1080, 2400)
    }
    
    fun configureForNightMode(imageCapture: ImageCapture): ImageCapture {
        // Configure for Sony IMX 766 night mode capabilities
        return ImageCapture.Builder()
            .setTargetResolution(getOptimalPhotoResolution())
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
            .setFlashMode(ImageCapture.FLASH_MODE_OFF) // Use sensor capabilities instead
            .setJpegQuality(95)
            .build()
    }
    
    fun configureMacroMode(): CameraSelector {
        // Use Samsung JN1 ultrawide for macro (4cm close focus)
        return CameraSelector.Builder()
            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
            .addCameraFilter { cameraInfos ->
                cameraInfos.filter { cameraInfo ->
                    try {
                        val camera2Info = Camera2CameraInfo.from(cameraInfo)
                        val cameraId = camera2Info.cameraId
                        cameraId == ultraWideCameraId
                    } catch (e: Exception) {
                        false
                    }
                }
            }
            .build()
    }
    
    fun getSnapdragon778GOptimizations(): Map<String, Any> {
        return mapOf(
            // Snapdragon 778G+ specific optimizations
            "useGpuAcceleration" to true,
            "enableHexagonDsp" to true, // For AI processing
            "maxConcurrentStreams" to 3, // 778G+ can handle multiple streams
            "preferredColorSpace" to "P3", // Wide color gamut support
            "hdrCapable" to true,
            "maxVideoFrameRate" to 60,
            "maxPhotoResolution" to "50MP",
            "aiEnhancementSupport" to true
        )
    }
    
    fun detectLightConditions(): LightCondition {
        // Use camera sensor to detect lighting conditions
        // This would integrate with the camera's light sensor
        return LightCondition.NORMAL // Placeholder
    }
    
    fun shouldSuggestNightMode(): Boolean {
        return detectLightConditions() == LightCondition.LOW_LIGHT
    }
    
    fun getOptimalZoomRanges(): ZoomRanges {
        return ZoomRanges(
            mainCamera = 1f..5f, // Sony IMX 766 digital zoom
            ultraWide = 0.6f..1f, // Samsung JN1 ultrawide
            macro = 1f..2f // Macro mode on ultrawide
        )
    }
    
    fun configureAutoFocus(): FocusMeteringAction? {
        // Configure PDAF for Sony IMX 766
        return null // Would configure phase detection autofocus
    }
    
    fun getOptimalISORange(): IntRange {
        return try {
            val characteristics = cameraManager.getCameraCharacteristics(mainCameraId)
            val isoRange = characteristics.get(CameraCharacteristics.SENSOR_INFO_SENSITIVITY_RANGE)
            isoRange?.let { IntRange(it.lower, it.upper) } ?: IntRange(100, 3200)
        } catch (e: Exception) {
            IntRange(100, 3200) // Safe default for Sony IMX 766
        }
    }
    
    fun getOptimalExposureRange(): IntRange {
        return try {
            val characteristics = cameraManager.getCameraCharacteristics(mainCameraId)
            val exposureRange = characteristics.get(CameraCharacteristics.CONTROL_AE_COMPENSATION_RANGE)
            exposureRange?.let { IntRange(it.lower, it.upper) } ?: IntRange(-12, 12)
        } catch (e: Exception) {
            IntRange(-12, 12) // Safe default
        }
    }
    
    fun enableOIS(): Boolean {
        return try {
            val characteristics = cameraManager.getCameraCharacteristics(mainCameraId)
            val stabilization = characteristics.get(CameraCharacteristics.LENS_INFO_AVAILABLE_OPTICAL_STABILIZATION)
            stabilization?.contains(CameraMetadata.LENS_OPTICAL_STABILIZATION_MODE_ON) == true
        } catch (e: Exception) {
            false
        }
    }
    
    fun getAvailableFilters(): List<FilterType> {
        // Return filters optimized for Snapdragon 778G+ ISP
        return listOf(
            FilterType.VIVID,
            FilterType.DRAMATIC,
            FilterType.BRILLIANT,
            FilterType.MONO,
            FilterType.SILVERTONE,
            FilterType.NOIR
        )
    }
}

enum class LightCondition {
    BRIGHT,
    NORMAL,
    LOW_LIGHT,
    VERY_LOW_LIGHT
}

data class ZoomRanges(
    val mainCamera: ClosedFloatingPointRange<Float>,
    val ultraWide: ClosedFloatingPointRange<Float>,
    val macro: ClosedFloatingPointRange<Float>
)

// Extension functions for Nothing Phone One specific features
fun CameraController.applyNothingPhoneOptimizations(context: Context) {
    val optimizations = NothingPhoneOptimizations(context)
    
    // Apply Snapdragon 778G+ specific settings
    val settings = optimizations.getSnapdragon778GOptimizations()
    
    // Configure for optimal performance on Nothing Phone One
    // This would apply the optimizations to the camera controller
}

fun FilterProcessor.optimizeForSnapdragon778G(): FilterProcessor {
    // Use Snapdragon 778G+ GPU acceleration for filter processing
    return this
}
