1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.camera"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Camera hardware requirements -->
12    <uses-feature
12-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:5-88
13        android:name="android.hardware.camera.any"
13-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:19-61
14        android:required="true" />
14-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:62-85
15    <uses-feature
15-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:5-84
16        android:name="android.hardware.camera"
16-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:19-57
17        android:required="true" />
17-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:58-81
18    <uses-feature
18-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:5-94
19        android:name="android.hardware.camera.autofocus"
19-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:19-67
20        android:required="true" />
20-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:68-91
21    <uses-feature
21-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:5-91
22        android:name="android.hardware.camera.flash"
22-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:19-63
23        android:required="false" />
23-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:64-88
24
25    <!-- Required permissions -->
26    <uses-permission android:name="android.permission.CAMERA" />
26-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:12:5-65
26-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:12:22-62
27    <uses-permission android:name="android.permission.RECORD_AUDIO" />
27-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:13:5-71
27-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:13:22-68
28    <uses-permission android:name="android.permission.VIBRATE" />
28-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:14:5-66
28-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:14:22-63
29
30    <!-- Storage permissions for Android 13+ -->
31    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
31-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:17:5-76
31-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:17:22-73
32    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
32-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:18:5-75
32-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:18:22-72
33    <uses-permission
33-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:19:5-20:38
34        android:name="android.permission.READ_EXTERNAL_STORAGE"
34-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:19:22-77
35        android:maxSdkVersion="32" />
35-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:20:9-35
36    <uses-permission
36-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:21:5-22:38
37        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
37-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:21:22-78
38        android:maxSdkVersion="28" />
38-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:22:9-35
39
40    <!-- Nothing Phone specific permissions -->
41    <uses-permission android:name="com.nothing.ketchum.permission.LIGHTS" />
41-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:25:5-77
41-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:25:22-74
42
43    <!-- Network permission for potential cloud processing -->
44    <uses-permission android:name="android.permission.INTERNET" />
44-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:28:5-67
44-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:28:22-64
45    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
45-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:29:5-79
45-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:29:22-76
46
47    <!-- For Android 11+ package visibility -->
48    <queries>
48-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:32:5-39:15
49        <intent>
49-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:33:9-35:18
50            <action android:name="android.media.action.IMAGE_CAPTURE" />
50-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:34:13-73
50-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:34:21-70
51        </intent>
52        <intent>
52-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:36:9-38:18
53            <action android:name="android.intent.action.GET_CONTENT" />
53-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:37:13-72
53-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:37:21-69
54        </intent>
55        <intent>
55-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:23:9-25:18
56            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
56-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:13-86
56-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:21-83
57        </intent>
58    </queries>
59
60    <permission
60-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
61        android:name="com.example.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
61-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
62        android:protectionLevel="signature" />
62-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
63
64    <uses-permission android:name="com.example.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
64-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
64-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
65
66    <application
66-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:41:5-63:19
67        android:allowBackup="true"
67-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:42:9-35
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
69        android:dataExtractionRules="@xml/data_extraction_rules"
69-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:43:9-65
70        android:debuggable="true"
71        android:extractNativeLibs="false"
72        android:fullBackupContent="@xml/backup_rules"
72-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:44:9-54
73        android:icon="@mipmap/ic_launcher"
73-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:45:9-43
74        android:label="@string/app_name"
74-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:46:9-41
75        android:roundIcon="@mipmap/ic_launcher_round"
75-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:47:9-54
76        android:supportsRtl="true"
76-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:48:9-35
77        android:theme="@style/Theme.Camera" >
77-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:49:9-44
78        <activity
78-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:52:9-62:20
79            android:name="com.example.camera.MainActivity"
79-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:53:13-41
80            android:exported="true"
80-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:54:13-36
81            android:label="@string/app_name"
81-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:55:13-45
82            android:screenOrientation="portrait"
82-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:57:13-49
83            android:theme="@style/Theme.Camera" >
83-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:56:13-48
84            <intent-filter>
84-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:58:13-61:29
85                <action android:name="android.intent.action.MAIN" />
85-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:59:17-69
85-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:59:25-66
86
87                <category android:name="android.intent.category.LAUNCHER" />
87-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:60:17-77
87-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:60:27-74
88            </intent-filter>
89        </activity>
90
91        <uses-library
91-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:29:9-31:40
92            android:name="androidx.camera.extensions.impl"
92-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:30:13-59
93            android:required="false" />
93-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:31:13-37
94
95        <service
95-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
96            android:name="androidx.camera.core.impl.MetadataHolderService"
96-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
97            android:enabled="false"
97-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
98            android:exported="false" >
98-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
99            <meta-data
99-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
100                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
100-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
101                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
101-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
102        </service>
103
104        <activity
104-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:23:9-25:39
105            android:name="androidx.activity.ComponentActivity"
105-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:24:13-63
106            android:exported="true" />
106-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:25:13-36
107        <activity
107-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
108            android:name="androidx.compose.ui.tooling.PreviewActivity"
108-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
109            android:exported="true" />
109-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
110
111        <provider
111-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
112            android:name="androidx.startup.InitializationProvider"
112-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
113            android:authorities="com.example.camera.androidx-startup"
113-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
114            android:exported="false" >
114-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
115            <meta-data
115-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.emoji2.text.EmojiCompatInitializer"
116-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
117                android:value="androidx.startup" />
117-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
118            <meta-data
118-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
119-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
120                android:value="androidx.startup" />
120-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
123                android:value="androidx.startup" />
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
124        </provider>
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145    </application>
146
147</manifest>
