1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.camera"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Camera hardware requirements -->
12    <uses-feature
12-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:5-88
13        android:name="android.hardware.camera.any"
13-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:19-61
14        android:required="true" />
14-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:6:62-85
15    <uses-feature
15-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:5-84
16        android:name="android.hardware.camera"
16-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:19-57
17        android:required="true" />
17-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:7:58-81
18    <uses-feature
18-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:5-94
19        android:name="android.hardware.camera.autofocus"
19-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:19-67
20        android:required="true" />
20-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:8:68-91
21    <uses-feature
21-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:5-91
22        android:name="android.hardware.camera.flash"
22-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:19-63
23        android:required="false" />
23-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:9:64-88
24
25    <!-- Required permissions -->
26    <uses-permission android:name="android.permission.CAMERA" />
26-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:12:5-65
26-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:12:22-62
27    <uses-permission android:name="android.permission.RECORD_AUDIO" />
27-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:13:5-71
27-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:13:22-68
28    <uses-permission android:name="android.permission.VIBRATE" />
28-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:14:5-66
28-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:14:22-63
29
30    <!-- Storage permissions for Android 13+ -->
31    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
31-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:17:5-76
31-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:17:22-73
32    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
32-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:18:5-75
32-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:18:22-72
33    <uses-permission
33-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:19:5-20:38
34        android:name="android.permission.READ_EXTERNAL_STORAGE"
34-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:19:22-77
35        android:maxSdkVersion="32" />
35-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:20:9-35
36    <uses-permission
36-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:21:5-22:38
37        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
37-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:21:22-78
38        android:maxSdkVersion="28" />
38-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:22:9-35
39
40    <!-- For saving to public gallery -->
41    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
41-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:25:5-26:40
41-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:25:22-79
42
43    <!-- Nothing Phone specific permissions -->
44    <uses-permission android:name="com.nothing.ketchum.permission.LIGHTS" />
44-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:29:5-77
44-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:29:22-74
45
46    <!-- Network permission for potential cloud processing -->
47    <uses-permission android:name="android.permission.INTERNET" />
47-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:32:5-67
47-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:32:22-64
48    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
48-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:33:5-79
48-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:33:22-76
49
50    <!-- For Android 11+ package visibility -->
51    <queries>
51-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:36:5-43:15
52        <intent>
52-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:37:9-39:18
53            <action android:name="android.media.action.IMAGE_CAPTURE" />
53-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:38:13-73
53-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:38:21-70
54        </intent>
55        <intent>
55-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:40:9-42:18
56            <action android:name="android.intent.action.GET_CONTENT" />
56-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:41:13-72
56-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:41:21-69
57        </intent>
58        <intent>
58-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:23:9-25:18
59            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
59-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:13-86
59-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:24:21-83
60        </intent>
61    </queries>
62
63    <permission
63-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
64        android:name="com.example.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.example.camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
68
69    <application
69-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:45:5-67:19
70        android:allowBackup="true"
70-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:46:9-35
71        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
71-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec9d6daadf0a281028b7cf0e9684049e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
72        android:dataExtractionRules="@xml/data_extraction_rules"
72-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:47:9-65
73        android:debuggable="true"
74        android:extractNativeLibs="false"
75        android:fullBackupContent="@xml/backup_rules"
75-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:48:9-54
76        android:icon="@mipmap/ic_launcher"
76-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:49:9-43
77        android:label="@string/app_name"
77-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:50:9-41
78        android:roundIcon="@mipmap/ic_launcher_round"
78-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:51:9-54
79        android:supportsRtl="true"
79-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:52:9-35
80        android:theme="@style/Theme.Camera" >
80-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:53:9-44
81        <activity
81-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:56:9-66:20
82            android:name="com.example.camera.MainActivity"
82-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:57:13-41
83            android:exported="true"
83-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:58:13-36
84            android:label="@string/app_name"
84-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:59:13-45
85            android:screenOrientation="portrait"
85-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:61:13-49
86            android:theme="@style/Theme.Camera" >
86-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:60:13-48
87            <intent-filter>
87-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:62:13-65:29
88                <action android:name="android.intent.action.MAIN" />
88-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:63:17-69
88-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:63:25-66
89
90                <category android:name="android.intent.category.LAUNCHER" />
90-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:64:17-77
90-->C:\Users\<USER>\AndroidStudioProjects\Camera\app\src\main\AndroidManifest.xml:64:27-74
91            </intent-filter>
92        </activity>
93
94        <uses-library
94-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:29:9-31:40
95            android:name="androidx.camera.extensions.impl"
95-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:30:13-59
96            android:required="false" />
96-->[androidx.camera:camera-extensions:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc32765e556e609435378e6806409ceb\transformed\camera-extensions-1.3.0\AndroidManifest.xml:31:13-37
97
98        <service
98-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:24:9-33:19
99            android:name="androidx.camera.core.impl.MetadataHolderService"
99-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:25:13-75
100            android:enabled="false"
100-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:26:13-36
101            android:exported="false" >
101-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:27:13-37
102            <meta-data
102-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:30:13-32:89
103                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
103-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:31:17-103
104                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
104-->[androidx.camera:camera-camera2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49e051937617a7057ff30bdd9586324c\transformed\camera-camera2-1.3.0\AndroidManifest.xml:32:17-86
105        </service>
106
107        <activity
107-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:23:9-25:39
108            android:name="androidx.activity.ComponentActivity"
108-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:24:13-63
109            android:exported="true" />
109-->[androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a326ed00e36114f1c8975e018d7e0e2\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:25:13-36
110        <activity
110-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
111            android:name="androidx.compose.ui.tooling.PreviewActivity"
111-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
112            android:exported="true" />
112-->[androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4351cefe15584cff224facdb1b788fa6\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
113
114        <provider
114-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
115            android:name="androidx.startup.InitializationProvider"
115-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
116            android:authorities="com.example.camera.androidx-startup"
116-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
117            android:exported="false" >
117-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
118            <meta-data
118-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.emoji2.text.EmojiCompatInitializer"
119-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
120                android:value="androidx.startup" />
120-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68e33489dc2b342e833601ddba2bf53f\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
122-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
123                android:value="androidx.startup" />
123-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c19f55776b92be611904e8e546c7a4fa\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
124            <meta-data
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
126                android:value="androidx.startup" />
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
127        </provider>
128
129        <receiver
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
130            android:name="androidx.profileinstaller.ProfileInstallReceiver"
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
131            android:directBootAware="false"
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
132            android:enabled="true"
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
133            android:exported="true"
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
134            android:permission="android.permission.DUMP" >
134-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
136                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
139                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
139-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
142                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
143            </intent-filter>
144            <intent-filter>
144-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
145                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
145-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
145-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95b305250bf4171435d52c1d5236e6e6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
146            </intent-filter>
147        </receiver>
148    </application>
149
150</manifest>
