{"logs": [{"outputFile": "com.example.camera.app-mergeDebugResources-52:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3242,3341,3443,3542,3642,3743,3849,9067", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3336,3438,3537,3637,3738,3844,3961,9163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,969,1035,1116,1198,1272,1347,1414", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,964,1030,1111,1193,1267,1342,1409,1526"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3966,4053,7668,7762,7936,8101,8183,8286,8371,8454,8521,8587,8668,8913,9263,9338,9405", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "4048,4127,7757,7855,8017,8178,8281,8366,8449,8516,8582,8663,8745,8982,9333,9400,9517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,885,978,1072,1164,1258,1361,1456,1553,1647,1740,1830,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,77,92,93,91,93,102,94,96,93,92,89,80,107,103,97,105,104,104,156,100,80", "endOffsets": "210,311,422,506,607,722,802,880,973,1067,1159,1253,1356,1451,1548,1642,1735,1825,1906,2014,2118,2216,2322,2427,2532,2689,2790,2871"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,885,978,1072,1164,1258,1361,1456,1553,1647,1740,1830,1911,2019,2123,2221,2327,2432,2537,2694,8832", "endColumns": "109,100,110,83,100,114,79,77,92,93,91,93,102,94,96,93,92,89,80,107,103,97,105,104,104,156,100,80", "endOffsets": "210,311,422,506,607,722,802,880,973,1067,1159,1253,1356,1451,1548,1642,1735,1825,1906,2014,2118,2216,2322,2427,2532,2689,2790,8908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,277,387,502,578,670,781,916,1028,1159,1240,1339,1427,1520,1630,1749,1853,1982,2109,2227,2388,2506,2615,2728,2842,2931,3025,3142,3270,3373,3472,3573,3699,3831,3933,4038,4114,4193,4275,4355,4450,4528,4608,4705,4802,4895,4991,5074,5174,5270,5368,5484,5562,5662", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "162,272,382,497,573,665,776,911,1023,1154,1235,1334,1422,1515,1625,1744,1848,1977,2104,2222,2383,2501,2610,2723,2837,2926,3020,3137,3265,3368,3467,3568,3694,3826,3928,4033,4109,4188,4270,4350,4445,4523,4603,4700,4797,4890,4986,5069,5169,5265,5363,5479,5557,5657,5751"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2795,2907,3017,3127,4132,4208,4300,4411,4546,4658,4789,4870,4969,5057,5150,5260,5379,5483,5612,5739,5857,6018,6136,6245,6358,6472,6561,6655,6772,6900,7003,7102,7203,7329,7461,7563,7860,8022,8750,8987,9168,9522,9600,9680,9777,9874,9967,10063,10146,10246,10342,10440,10556,10634,10734", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "2902,3012,3122,3237,4203,4295,4406,4541,4653,4784,4865,4964,5052,5145,5255,5374,5478,5607,5734,5852,6013,6131,6240,6353,6467,6556,6650,6767,6895,6998,7097,7198,7324,7456,7558,7663,7931,8096,8827,9062,9258,9595,9675,9772,9869,9962,10058,10141,10241,10337,10435,10551,10629,10729,10823"}}]}]}