{"logs": [{"outputFile": "com.example.camera.app-mergeReleaseResources-48:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,919,1029,1136,1242,1351,1463,1566,1678,1785,1890,1990,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,80,109,106,105,108,111,102,111,106,104,99,83,108,110,98,110,106,104,173,98,81", "endOffsets": "220,330,439,525,629,751,833,914,1024,1131,1237,1346,1458,1561,1673,1780,1885,1985,2069,2178,2289,2388,2499,2606,2711,2885,2984,3066"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,919,1029,1136,1242,1351,1463,1566,1678,1785,1890,1990,2074,2183,2294,2393,2504,2611,2716,2890,9229", "endColumns": "119,109,108,85,103,121,81,80,109,106,105,108,111,102,111,106,104,99,83,108,110,98,110,106,104,173,98,81", "endOffsets": "220,330,439,525,629,751,833,914,1024,1131,1237,1346,1458,1561,1673,1780,1885,1985,2069,2178,2289,2388,2499,2606,2711,2885,2984,9306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec9d6daadf0a281028b7cf0e9684049e\\transformed\\core-1.10.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3467,3565,3667,3767,3868,3975,4083,9468", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3560,3662,3762,3863,3970,4078,4193,9564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d923302443db812770a531d84fe1bab8\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1002,1072,1157,1244,1317,1395,1463", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,997,1067,1152,1239,1312,1390,1458,1580"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4198,4296,8036,8132,8314,8480,8566,8654,8747,8831,8901,8971,9056,9311,9676,9754,9822", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "4291,4380,8127,8230,8399,8561,8649,8742,8826,8896,8966,9051,9138,9379,9749,9817,9939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a0651e95cd0e8d6c115791cc868f86d2\\transformed\\material3-1.1.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,301,412,533,610,706,820,954,1068,1211,1293,1387,1477,1574,1689,1809,1908,2042,2169,2303,2482,2610,2725,2849,2966,3057,3152,3269,3400,3498,3608,3710,3840,3981,4086,4184,4263,4339,4425,4509,4616,4692,4775,4866,4966,5053,5149,5234,5337,5435,5532,5681,5757,5858", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "173,296,407,528,605,701,815,949,1063,1206,1288,1382,1472,1569,1684,1804,1903,2037,2164,2298,2477,2605,2720,2844,2961,3052,3147,3264,3395,3493,3603,3705,3835,3976,4081,4179,4258,4334,4420,4504,4611,4687,4770,4861,4961,5048,5144,5229,5332,5430,5527,5676,5752,5853,5948"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2989,3112,3235,3346,4385,4462,4558,4672,4806,4920,5063,5145,5239,5329,5426,5541,5661,5760,5894,6021,6155,6334,6462,6577,6701,6818,6909,7004,7121,7252,7350,7460,7562,7692,7833,7938,8235,8404,9143,9384,9569,9944,10020,10103,10194,10294,10381,10477,10562,10665,10763,10860,11009,11085,11186", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "3107,3230,3341,3462,4457,4553,4667,4801,4915,5058,5140,5234,5324,5421,5536,5656,5755,5889,6016,6150,6329,6457,6572,6696,6813,6904,6999,7116,7247,7345,7455,7557,7687,7828,7933,8031,8309,8475,9224,9463,9671,10015,10098,10189,10289,10376,10472,10557,10660,10758,10855,11004,11080,11181,11276"}}]}]}