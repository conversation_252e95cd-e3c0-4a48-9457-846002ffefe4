package com.example.camera

import android.content.Context
import android.os.VibrationEffect
import android.os.Vibrator
import androidx.camera.view.PreviewView
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.airbnb.lottie.compose.*
import kotlinx.coroutines.launch

@Composable
fun iPhoneCameraUI(
    cameraController: CameraController,
    vibrator: Vibrator,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()

    val modes = listOf("PHOTO", "VIDEO", "PORTRAIT", "CINEMATIC")
    var currentModeIndex by remember { mutableStateOf(0) }

    // Camera states with performance optimization
    var zoom by remember { mutableStateOf(1f) }
    val minZoom = 0.5f
    val maxZoom = 15f // iPhone 15 Pro style 15x zoom

    var currentFilter by remember { mutableStateOf(FilterType.VIVID) }
    var isNightModeDetected by remember { mutableStateOf(false) }
    var isMacroModeEnabled by remember { mutableStateOf(false) }
    var flashMode by remember { mutableStateOf(FlashMode.OFF) }
    var isRecording by remember { mutableStateOf(false) }

    // iPhone 15 Pro style zoom levels (cached for performance)
    val zoomLevels by remember(currentModeIndex) {
        mutableStateOf(cameraController.getAvailableZoomLevels())
    }

    // Update camera settings when they change
    LaunchedEffect(zoom) {
        cameraController.setZoom(zoom)
    }

    LaunchedEffect(currentFilter) {
        cameraController.setFilter(currentFilter)
    }

    LaunchedEffect(isMacroModeEnabled) {
        cameraController.enableMacroMode(isMacroModeEnabled)
    }

    LaunchedEffect(currentModeIndex) {
        val mode = when (modes[currentModeIndex]) {
            "PHOTO" -> CameraMode.PHOTO
            "VIDEO" -> CameraMode.VIDEO
            "PORTRAIT" -> CameraMode.PORTRAIT
            "CINEMATIC" -> CameraMode.CINEMATIC
            else -> CameraMode.PHOTO
        }
        cameraController.setMode(mode)
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            // Optimized gesture handling for smooth zoom
            .pointerInput(currentModeIndex) {
                detectTransformGestures { _, _, zoomChange, _ ->
                    val newZoom = (zoom * zoomChange).coerceIn(minZoom, maxZoom)

                    // Throttle zoom updates for better performance
                    if (kotlin.math.abs(newZoom - zoom) > 0.1f) {
                        scope.launch {
                            cameraController.smoothZoomTo(newZoom)
                            zoom = newZoom

                            // Reduced haptic feedback for smoother experience
                            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                                vibrator.vibrate(VibrationEffect.createOneShot(5, VibrationEffect.DEFAULT_AMPLITUDE))
                            }
                        }
                    }
                }
            }
    ) {
        // Camera preview
        CameraPreviewView(
            cameraController = cameraController,
            modifier = Modifier.fillMaxSize()
        )

        // Top control bar with flash, HDR, timer, filters
        TopControlBar(
            currentFilter = currentFilter,
            onFilterChanged = { currentFilter = it },
            isNightModeDetected = isNightModeDetected,
            onNightModeToggle = { cameraController.enableNightMode(it) },
            flashMode = flashMode,
            onFlashToggle = {
                flashMode = cameraController.toggleFlash()
                // Haptic feedback for flash toggle
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    vibrator.vibrate(VibrationEffect.createOneShot(30, VibrationEffect.DEFAULT_AMPLITUDE))
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp)
        )

        // Mode selector
        ModeSelector(
            modes = modes,
            selectedIndex = currentModeIndex,
            onModeSelected = { idx ->
                currentModeIndex = idx
                // Reset zoom on mode change
                zoom = 1f

                // Haptic feedback
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
                }
            },
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 120.dp)
        )

        // Shutter button with video recording support
        ShutterButton(
            isRecording = isRecording,
            currentMode = modes[currentModeIndex],
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 40.dp),
            onCapture = {
                scope.launch {
                    // Haptic feedback for capture
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        vibrator.vibrate(VibrationEffect.createOneShot(100, VibrationEffect.DEFAULT_AMPLITUDE))
                    }

                    when (modes[currentModeIndex]) {
                        "VIDEO", "CINEMATIC" -> {
                            if (isRecording) {
                                cameraController.stopVideoRecording()
                                isRecording = false
                            } else {
                                val started = cameraController.startVideoRecording()
                                isRecording = started
                            }
                        }
                        else -> {
                            cameraController.capturePhoto()
                        }
                    }
                }
            }
        )

        // iPhone 15 Pro style zoom controls (different for portrait mode)
        val currentZoomLevels = cameraController.getAvailableZoomLevels()
        val isPortraitMode = modes[currentModeIndex] == "PORTRAIT"

        ZoomControls(
            currentZoom = zoom,
            zoomLevels = currentZoomLevels,
            isPortraitMode = isPortraitMode,
            onZoomChanged = { newZoom ->
                scope.launch {
                    cameraController.smoothZoomTo(newZoom)
                    zoom = newZoom

                    // Haptic feedback for zoom
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        vibrator.vibrate(VibrationEffect.createOneShot(20, VibrationEffect.DEFAULT_AMPLITUDE))
                    }
                }
            },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(end = 16.dp, bottom = 200.dp)
        )

        // Zoom indicator
        if (zoom != 1f) {
            ZoomIndicator(
                zoom = zoom,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 60.dp)
            )
        }

        // Portrait mode indicator
        if (isPortraitMode) {
            PortraitModeIndicator(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(start = 16.dp, top = 60.dp)
            )
        }

        // Macro mode toggle (when ultrawide is selected and not in portrait mode)
        if (zoom <= 1.2f && !isPortraitMode) {
            MacroModeToggle(
                enabled = isMacroModeEnabled,
                onToggle = { isMacroModeEnabled = it },
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 16.dp, bottom = 200.dp)
            )
        }
    }
}

@Composable
fun CameraPreviewView(
    cameraController: CameraController,
    modifier: Modifier = Modifier
) {
    val previewView = remember { PreviewView(LocalContext.current) }

    LaunchedEffect(cameraController) {
        cameraController.setSurfaceProvider(previewView.surfaceProvider)
    }

    AndroidView(
        factory = { previewView },
        modifier = modifier
    )
}

@Composable
fun TopControlBar(
    currentFilter: FilterType,
    onFilterChanged: (FilterType) -> Unit,
    isNightModeDetected: Boolean,
    onNightModeToggle: (Boolean) -> Unit,
    flashMode: FlashMode,
    onFlashToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Filter selector
        FilterButton(
            currentFilter = currentFilter,
            onFilterChanged = onFilterChanged
        )

        // Night mode indicator/toggle
        if (isNightModeDetected) {
            IconButton(onClick = { onNightModeToggle(true) }) {
                Text("🌙", style = MaterialTheme.typography.headlineSmall)
            }
        }

        // iPhone 15 Pro style flash toggle (Flash -> Glyph -> Off)
        FlashButton(
            flashMode = flashMode,
            onToggle = onFlashToggle
        )
    }
}

@Composable
fun ModeSelector(
    modes: List<String>,
    selectedIndex: Int,
    onModeSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.pointerInput(Unit) {
            detectHorizontalDragGestures { change, dragAmount ->
                change.consume()
                if (dragAmount > 20) {
                    onModeSelected((selectedIndex + 1).coerceAtMost(modes.lastIndex))
                } else if (dragAmount < -20) {
                    onModeSelected((selectedIndex - 1).coerceAtLeast(0))
                }
            }
        },
        horizontalArrangement = Arrangement.Center
    ) {
        modes.forEachIndexed { index, mode ->
            Text(
                text = mode,
                color = if (index == selectedIndex) Color.White else Color.Gray,
                modifier = Modifier.padding(horizontal = 12.dp)
            )
        }
    }
}

@Composable
fun ShutterButton(
    isRecording: Boolean = false,
    currentMode: String = "PHOTO",
    modifier: Modifier = Modifier,
    onCapture: () -> Unit
) {
    // Animated shutter button for video recording
    val animatedSize by animateFloatAsState(
        targetValue = if (isRecording) 0.7f else 1f,
        animationSpec = tween(300)
    )

    val buttonColor = when {
        isRecording -> Color.Red
        currentMode in listOf("VIDEO", "CINEMATIC") -> Color.Red.copy(alpha = 0.8f)
        else -> Color.White
    }

    val buttonShape = when {
        isRecording -> RoundedCornerShape(12.dp) // Square for recording
        else -> CircleShape // Circle for photo
    }

    // iPhone-style shutter button with video recording animation
    Box(
        modifier = modifier
            .size(80.dp)
            .background(Color.White.copy(alpha = 0.3f), CircleShape)
            .clickable { onCapture() },
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .size((60.dp * animatedSize))
                .background(buttonColor, buttonShape)
        )

        // Recording indicator
        if (isRecording) {
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .background(Color.White, RoundedCornerShape(4.dp))
            )
        }
    }
}

@Composable
fun FilterButton(
    currentFilter: FilterType,
    onFilterChanged: (FilterType) -> Unit,
    modifier: Modifier = Modifier
) {
    var showFilterMenu by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        TextButton(
            onClick = { showFilterMenu = true }
        ) {
            Text(
                text = when (currentFilter) {
                    FilterType.VIVID -> "Vivid"
                    FilterType.DRAMATIC -> "Dramatic"
                    FilterType.BRILLIANT -> "Brilliant"
                    FilterType.MONO -> "Mono"
                    FilterType.SILVERTONE -> "Silver"
                    FilterType.NOIR -> "Noir"
                    FilterType.NONE -> "Natural"
                },
                color = Color.White
            )
        }

        DropdownMenu(
            expanded = showFilterMenu,
            onDismissRequest = { showFilterMenu = false }
        ) {
            FilterType.values().forEach { filter ->
                DropdownMenuItem(
                    text = {
                        Text(when (filter) {
                            FilterType.VIVID -> "Vivid"
                            FilterType.DRAMATIC -> "Dramatic"
                            FilterType.BRILLIANT -> "Brilliant"
                            FilterType.MONO -> "Mono"
                            FilterType.SILVERTONE -> "Silvertone"
                            FilterType.NOIR -> "Noir"
                            FilterType.NONE -> "Natural"
                        })
                    },
                    onClick = {
                        onFilterChanged(filter)
                        showFilterMenu = false
                    }
                )
            }
        }
    }
}

@Composable
fun ZoomIndicator(
    zoom: Float,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color.Black.copy(alpha = 0.7f))
    ) {
        Text(
            text = "${String.format("%.1f", zoom)}x",
            color = Color.White,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp)
        )
    }
}

@Composable
fun MacroModeToggle(
    enabled: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = { onToggle(!enabled) },
        modifier = modifier
    ) {
        Text(
            text = "🔍",
            style = MaterialTheme.typography.headlineSmall,
            color = if (enabled) Color.Yellow else Color.White
        )
    }
}

@Composable
fun FlashButton(
    flashMode: FlashMode,
    onToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onToggle,
        modifier = modifier
    ) {
        Text(
            text = when (flashMode) {
                FlashMode.OFF -> "⚡"
                FlashMode.ON -> "⚡"
                FlashMode.GLYPH -> "◉" // Glyph indicator
                FlashMode.AUTO -> "⚡"
            },
            style = MaterialTheme.typography.headlineSmall,
            color = when (flashMode) {
                FlashMode.OFF -> Color.White
                FlashMode.ON -> Color.Yellow
                FlashMode.GLYPH -> Color.Cyan // Distinctive color for Glyph mode
                FlashMode.AUTO -> Color.White
            }
        )
    }
}

@Composable
fun ZoomControls(
    currentZoom: Float,
    zoomLevels: List<Float>,
    isPortraitMode: Boolean = false,
    onZoomChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    if (isPortraitMode) {
        // iPhone 15 Pro Portrait mode: Horizontal layout for 1x, 2x, 3x
        Row(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            zoomLevels.forEach { zoomLevel ->
                val isSelected = kotlin.math.abs(currentZoom - zoomLevel) < 0.1f

                Card(
                    modifier = Modifier
                        .clickable { onZoomChanged(zoomLevel) },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isSelected)
                            Color.Yellow.copy(alpha = 0.9f) // Portrait mode uses yellow accent
                        else
                            Color.Black.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        text = "${zoomLevel.toInt()}x",
                        color = if (isSelected) Color.Black else Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                    )
                }
            }
        }
    } else {
        // Regular mode: Vertical layout for all zoom levels
        Column(
            modifier = modifier,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            zoomLevels.forEach { zoomLevel ->
                val isSelected = kotlin.math.abs(currentZoom - zoomLevel) < 0.1f

                Card(
                    modifier = Modifier
                        .padding(vertical = 2.dp)
                        .clickable { onZoomChanged(zoomLevel) },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isSelected)
                            Color.White.copy(alpha = 0.9f)
                        else
                            Color.Black.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        text = when {
                            zoomLevel < 1f -> "${(zoomLevel * 10).toInt() / 10f}x"
                            zoomLevel == 1f -> "1x"
                            zoomLevel < 10f -> "${zoomLevel.toInt()}x"
                            else -> "${zoomLevel.toInt()}x"
                        },
                        color = if (isSelected) Color.Black else Color.White,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun PortraitModeIndicator(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Yellow.copy(alpha = 0.9f)
        )
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "📷",
                style = MaterialTheme.typography.bodyMedium
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "Portrait",
                color = Color.Black,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}