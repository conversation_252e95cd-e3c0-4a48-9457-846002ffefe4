package com.example.cameraxa<PERSON>

import android.Manifest
import android.content.Context
import android.util.Size
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import com.airbnb.lottie.compose.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun iPhoneCameraUI(
    onCapturePhoto: () -> Unit,
    onStartVideoRecording: () -> Unit,
    onStopVideoRecording: () -> Unit,
    onModeSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    val modes = listOf("PHOTO", "VIDEO", "PORTRAIT")
    var currentModeIndex by remember { mutableStateOf(0) }

    // Zoom state
    var zoom by remember { mutableStateOf(1f) }
    val minZoom = 1f
    val maxZoom = 5f // Customize max zoom level

    // Apply vivid filter state
    var isVividFilterOn by remember { mutableStateOf(true) }

    Box(
        modifier = modifier
            .fillMaxSize()
            // Gesture for zoom (pinch & scroll)
            .pointerInput(Unit) {
                detectTransformGestures { _, pan, zoomChange, _ ->
                    val newZoom = (zoom * zoomChange).coerceIn(minZoom, maxZoom)
                    zoom = newZoom
                }
            }
            // Scroll-to-zoom with horizontal drag (optional)
            .pointerInput(Unit) {
                detectHorizontalDragGestures { _, dragAmount ->
                    val zoomChange = if (dragAmount > 0) 0.95f else 1.05f
                    zoom = (zoom * zoomChange).coerceIn(minZoom, maxZoom)
                }
            }
    ) {
        // Camera preview with zoom and filter applied
        CameraPreviewView(
            modifier = Modifier.fillMaxSize(),
            zoom = zoom,
            filter = if (isVividFilterOn) FilterType.VIVID else FilterType.NONE,
            context = context
        )

        TopControlBar(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .padding(horizontal = 16.dp, vertical = 8.dp)
        )

        ModeSelector(
            modes = modes,
            selectedIndex = currentModeIndex,
            onModeSelected = { idx ->
                currentModeIndex = idx
                onModeSelected(modes[idx])
                // Reset zoom on mode change optionally
                zoom = 1f
            },
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 100.dp)
        )

        ShutterButton(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 24.dp),
            onCapture = {
                if (modes[currentModeIndex] == "VIDEO") {
                    onStartVideoRecording()
                } else {
                    onCapturePhoto()
                }
            }
        )

        ProControlsPanel(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(16.dp)
        )
    }
}

@Composable
fun TopControlBar(modifier: Modifier = Modifier) {
    // Example top bar with dummy buttons for Flash, HDR, Timer
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        TextButton(onClick = {/* Toggle flash */}) { Text("Flash") }
        TextButton(onClick = {/* Toggle HDR */}) { Text("HDR") }
        TextButton(onClick = {/* Set timer */}) { Text("Timer") }
    }
}

@Composable
fun ModeSelector(
    modes: List<String>,
    selectedIndex: Int,
    onModeSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.pointerInput(Unit) {
            detectHorizontalDragGestures { change, dragAmount ->
                change.consume()
                if (dragAmount > 20) {
                    onModeSelected((selectedIndex + 1).coerceAtMost(modes.lastIndex))
                } else if (dragAmount < -20) {
                    onModeSelected((selectedIndex - 1).coerceAtLeast(0))
                }
            }
        },
        horizontalArrangement = Arrangement.Center
    ) {
        modes.forEachIndexed { index, mode ->
            Text(
                text = mode,
                color = if (index == selectedIndex) Color.White else Color.Gray,
                modifier = Modifier.padding(horizontal = 12.dp)
            )
        }
    }
}

@Composable
fun ShutterButton(
    modifier: Modifier = Modifier,
    onCapture: () -> Unit
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("shutter_pulse.json"))
    val progress by animateLottieCompositionAsState(
        composition,
        iterations = LottieConstants.IterateForever
    )

    Box(
        modifier = modifier
            .size(80.dp)
            .background(Color.White.copy(alpha = 0.3f), CircleShape)
            .padding(12.dp)
            .background(Color.White, CircleShape)
            .clickable { onCapture() }
    ) {
        LottieAnimation(
            composition = composition,
            progress = progress,
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Composable
fun ProControlsPanel(modifier: Modifier = Modifier) {
    var focus by remember { mutableStateOf(0.5f) }
    var exposure by remember { mutableStateOf(0.5f) }
    var whiteBalance by remember { mutableStateOf(0.5f) }

    Column(modifier = modifier) {
        Text("Pro Controls", color = Color.White)
        Slider(value = focus, onValueChange = { focus = it }, modifier = Modifier.padding(vertical = 4.dp), valueRange = 0f..1f)
        Text("Focus", color = Color.White, style = MaterialTheme.typography.bodySmall)
        Slider(value = exposure, onValueChange = { exposure = it }, modifier = Modifier.padding(vertical = 4.dp), valueRange = 0f..1f)
        Text("Exposure", color = Color.White, style = MaterialTheme.typography.bodySmall)
        Slider(value = whiteBalance, onValueChange = { whiteBalance = it }, modifier = Modifier.padding(vertical = 4.dp), valueRange = 0f..1f)
        Text("White Balance", color = Color.White, style = MaterialTheme.typography.bodySmall)
    }
}