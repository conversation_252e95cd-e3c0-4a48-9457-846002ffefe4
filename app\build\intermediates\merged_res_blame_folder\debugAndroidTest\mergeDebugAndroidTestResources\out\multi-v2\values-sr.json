{"logs": [{"outputFile": "com.example.camera.test.app-mergeDebugAndroidTestResources-32:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\adb64f38986e7cdd49a1ca36d676d869\\transformed\\core-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,2023", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,2119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1156b8a02ed3a004521710322c86df6f\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,928,1015,1112,1213,1299,1376,1467,1559,1644,1715,1785,1865,1950,2124,2203,2273", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "923,1010,1107,1208,1294,1371,1462,1554,1639,1710,1780,1860,1945,2018,2198,2268,2386"}}]}]}